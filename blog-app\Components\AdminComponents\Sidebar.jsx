import { assets } from '@/Assets/assets'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter, usePathname } from 'next/navigation'
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

const Sidebar = () => {
    const router = useRouter();
    const pathname = usePathname();
    const [isExpanded, setIsExpanded] = useState(true);
    const [isAnimating, setIsAnimating] = useState(false);
    const [showText, setShowText] = useState(true);
    
    const toggleSidebar = () => {
        setIsAnimating(true);
        
        if (isExpanded) {
            // When collapsing, hide text immediately
            setShowText(false);
            setIsExpanded(false);
        } else {
            // When expanding, first expand the sidebar
            setIsExpanded(true);
            // Then show text with animation after a small delay
            setTimeout(() => {
                setShowText(true);
            }, 150);
        }
        
        // Reset animating state after animation completes
        setTimeout(() => {
            setIsAnimating(false);
        }, 300);
    };
    
    // Check if the current path matches the link
    const isActive = (path) => {
        return pathname === path;
    };
    
    return (
        <div className={`flex flex-col bg-slate-100 ${isExpanded ? 'w-28 sm:w-80' : 'w-20'} transition-all duration-300 min-h-screen`}>
            <div className={`py-3 border border-black flex items-center transition-all duration-300 ${isExpanded ? 'px-2 sm:pl-14 justify-between' : 'px-0 justify-center'} w-full`}> 
                {isExpanded && (
                    <Link href='/'>
                        <div className='flex items-center'>
                            <Image 
                                src={assets.logo} 
                                width={120} 
                                alt='Mr.Blogger' 
                                className='cursor-pointer transition-all duration-300' 
                            />
                        </div>
                    </Link>
                )}
                <button 
                    onClick={toggleSidebar}
                    className='p-2 hover:bg-slate-200 rounded-full transition-all ml-auto'
                >
                    <svg 
                        width="24" 
                        height="24" 
                        viewBox="0 0 24 24" 
                        fill="none" 
                        xmlns="http://www.w3.org/2000/svg"
                        className={`transform transition-transform ${!isExpanded ? 'rotate-180' : ''}`}
                    >
                        <path d="M15 18l-6-6 6-6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    </svg>
                </button>
            </div>
            <div className={`h-full relative py-12 border border-black border-t-0 transition-all duration-300 w-full`}> 
                <div className={`${isExpanded ? 'w-[50%] sm:w-[80%]' : 'w-[90%]'} absolute right-0 transition-all duration-300`}>
                    <Link href='/admin' className={`flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z" fill="currentColor"/>
                            </svg>
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Overview
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>
                    
                    <Link href='/admin/addBlog' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/addBlog') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <Image src={assets.blog_icon} alt='' width={28} />
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Blog Management
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>
                    
                    <Link href='/admin/addUser' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/addUser') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" fill="currentColor"/>
                            </svg>
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        User Management
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>

                    <Link href='/admin/settings' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/settings') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.12.21-.08.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z" fill="currentColor"/>
                            </svg>
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Settings
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>
                    
                    <Link href='/admin/feedback' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/feedback') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12h-2v-2h2v2zm0-4h-2V6h2v4z" fill="currentColor"/>
                            </svg>
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Feedback
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>

                    <Link href='/admin/subscriptions' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/subscriptions') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <Image src={assets.email_icon} alt='' width={28} />
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Subscriptions
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>

                    <Link href='/admin/reactions' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/reactions') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="currentColor"/>
                            </svg>
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Reactions
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>

                    <Link href='/admin/traffic' className={`mt-5 flex items-center border border-black gap-3 font-medium px-3 py-2 bg-white shadow-[-5px_5px_0px_#000000] ${isActive('/admin/traffic') ? 'bg-gray-100' : ''}`}>
                        <div className="flex-shrink-0 w-7 h-7 flex items-center justify-center">
                            <svg width="28" height="28" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 13h2v7H3v-7zm4-7h2v14H7V6zm4 3h2v11h-2V9zm4 4h2v7h-2v-7zm4-7h2v14h-2V6z" fill="currentColor"/>
                            </svg>
                        </div>
                        {isExpanded && (
                            <div className="w-full overflow-hidden">
                                {showText && (
                                    <span className="hidden sm:block sidebar-text-animate whitespace-nowrap">
                                        Traffic Analytics
                                    </span>
                                )}
                            </div>
                        )}
                    </Link>
                </div>
            </div>
        </div>
    )
}

export default Sidebar

