'use client'
import React, { useEffect, useState } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'
import Link from 'next/link'
import Image from 'next/image'

const ReactionsPage = () => {
  const [blogs, setBlogs] = useState([])
  const [loading, setLoading] = useState(true)
  const [sortOrder, setSortOrder] = useState('most') // 'most' or 'least'

  useEffect(() => {
    fetchBlogsWithLikes()
  }, [])

  const fetchBlogsWithLikes = async () => {
    try {
      setLoading(true)
      // Fetch all blogs
      const blogsResponse = await axios.get('/api/blog')
      const blogsData = blogsResponse.data.blogs || []
      
      // Fetch like counts for each blog
      const blogsWithLikes = await Promise.all(
        blogsData.map(async (blog) => {
          try {
            const likesResponse = await axios.get(`/api/blog/likes?id=${blog._id}`)
            return {
              ...blog,
              likeCount: likesResponse.data.success ? likesResponse.data.count : 0
            }
          } catch (error) {
            console.error(`Error fetching likes for blog ${blog._id}:`, error)
            return {
              ...blog,
              likeCount: 0
            }
          }
        })
      )
      
      setBlogs(blogsWithLikes)
    } catch (error) {
      console.error("Error fetching blogs with likes:", error)
      toast.error("Failed to load blogs with reactions")
    } finally {
      setLoading(false)
    }
  }

  const sortBlogs = (blogs, order) => {
    return [...blogs].sort((a, b) => {
      if (order === 'most') {
        return b.likeCount - a.likeCount
      } else {
        return a.likeCount - b.likeCount
      }
    })
  }

  const handleSortChange = (order) => {
    setSortOrder(order)
  }

  const sortedBlogs = sortBlogs(blogs, sortOrder)

  return (
    <div className='flex-1 pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className='text-2xl font-bold mb-6'>Blog Reactions</h1>
      
      <div className="mb-6">
        <div className="flex space-x-4 mb-4">
          <button 
            onClick={() => handleSortChange('most')}
            className={`px-4 py-2 rounded-md ${
              sortOrder === 'most' 
                ? 'bg-black text-white' 
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
            }`}
          >
            Most Liked
          </button>
          <button 
            onClick={() => handleSortChange('least')}
            className={`px-4 py-2 rounded-md ${
              sortOrder === 'least' 
                ? 'bg-black text-white' 
                : 'bg-gray-200 text-gray-800 hover:bg-gray-300'
            }`}
          >
            Least Liked
          </button>
        </div>
      </div>
      
      {loading ? (
        <div className="text-center py-8">
          <p>Loading blog reactions...</p>
        </div>
      ) : sortedBlogs.length === 0 ? (
        <div className="text-center py-8 bg-white rounded-lg shadow p-6">
          <p className="text-gray-500">No blogs found.</p>
        </div>
      ) : (
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Blog
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Author
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Likes
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sortedBlogs.map((blog) => (
                <tr key={blog._id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <img 
                          className="h-10 w-10 rounded-md object-cover" 
                          src={blog.image} 
                          alt={blog.title} 
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                          {blog.title}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <img 
                          className="h-8 w-8 rounded-full object-cover" 
                          src={blog.authorImg} 
                          alt={blog.author} 
                        />
                      </div>
                      <div className="ml-2 text-sm text-gray-900">{blog.author}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                      {blog.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(blog.date).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        width="20" 
                        height="20" 
                        viewBox="0 0 24 24" 
                        fill="currentColor"
                        className="text-red-500 mr-2"
                      >
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                      </svg>
                      <span className="text-sm font-medium">{blog.likeCount}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Link 
                      href={`/blogs/${blog._id}`} 
                      className="text-blue-600 hover:text-blue-900 mr-4"
                      target="_blank"
                    >
                      View
                    </Link>
                    <Link 
                      href={`/admin/editBlog/${blog._id}`} 
                      className="text-indigo-600 hover:text-indigo-900"
                    >
                      Edit
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  )
}

export default ReactionsPage