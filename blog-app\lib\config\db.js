import mongoose from "mongoose";

// Global variable to track connection state
let isConnected = false;

export const ConnectDB = async () => {
    try {
        // Check if already connected
        if (isConnected && mongoose.connections[0].readyState === 1) {
            console.log("DB Already Connected");
            return true;
        }

        const connectionString = process.env.MONGODB_URI || 'mongodb+srv://subhashanas:<EMAIL>/blog-app';

        // Improved connection options
        const options = {
            serverSelectionTimeoutMS: 30000, // Increased to 30s
            socketTimeoutMS: 60000, // Increased to 60s
            connectTimeoutMS: 30000, // Connection timeout
            maxPoolSize: 10, // Maximum number of connections
            minPoolSize: 2, // Minimum number of connections
            maxIdleTimeMS: 30000, // Close connections after 30s of inactivity
            bufferCommands: false, // Disable mongoose buffering
            bufferMaxEntries: 0, // Disable mongoose buffering
        };

        await mongoose.connect(connectionString, options);

        isConnected = true;
        console.log("DB Connected Successfully");

        // Handle connection events
        mongoose.connection.on('disconnected', () => {
            console.log('MongoDB disconnected');
            isConnected = false;
        });

        mongoose.connection.on('error', (err) => {
            console.error('MongoDB connection error:', err);
            isConnected = false;
        });

        return true;
    } catch (error) {
        console.error("DB Connection Error:", error.message);
        isConnected = false;
        return false;
    }
}