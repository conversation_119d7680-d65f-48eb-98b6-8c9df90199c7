import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import mongoose from 'mongoose';

export async function GET() {
  try {
    const startTime = Date.now();
    
    // Check database connection
    const isConnected = await ConnectDB();
    const connectionTime = Date.now() - startTime;
    
    // Get connection state
    const connectionState = mongoose.connections[0].readyState;
    const stateNames = {
      0: 'disconnected',
      1: 'connected',
      2: 'connecting',
      3: 'disconnecting'
    };
    
    // Basic database operation test
    let dbOperationWorking = false;
    let operationError = null;
    
    if (isConnected) {
      try {
        // Simple ping to test database operations
        await mongoose.connection.db.admin().ping();
        dbOperationWorking = true;
      } catch (error) {
        operationError = error.message;
      }
    }
    
    const healthData = {
      status: isConnected && dbOperationWorking ? 'healthy' : 'unhealthy',
      database: {
        connected: isConnected,
        connectionState: stateNames[connectionState] || 'unknown',
        connectionTime: `${connectionTime}ms`,
        operationsWorking: dbOperationWorking,
        operationError
      },
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development'
    };
    
    const statusCode = healthData.status === 'healthy' ? 200 : 503;
    
    return NextResponse.json(healthData, { status: statusCode });
  } catch (error) {
    return NextResponse.json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 503 });
  }
}
