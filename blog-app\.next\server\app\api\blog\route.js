"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/blog/route";
exports.ids = ["app/api/blog/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("fs/promises");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/blog/route.js */ \"(rsc)/./app/api/blog/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/blog/route\",\n        pathname: \"/api/blog\",\n        filename: \"route\",\n        bundlePath: \"app/api/blog/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\blog\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_blog_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/blog/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/blog/route.js":
/*!*******************************!*\
  !*** ./app/api/blog/route.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/models/BlogModel */ \"(rsc)/./lib/models/BlogModel.js\");\n/* harmony import */ var _lib_models_ImageModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/ImageModel */ \"(rsc)/./lib/models/ImageModel.js\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fs/promises */ \"fs/promises\");\n/* harmony import */ var fs_promises__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(fs_promises__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\nconst { NextResponse } = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/server.js\");\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst LoadDB = async ()=>{\n    await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n};\nLoadDB();\n// API Endpoint to get all blogs\nasync function GET(request) {\n    try {\n        // Ensure database connection\n        const isConnected = await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n        if (!isConnected) {\n            return NextResponse.json({\n                success: false,\n                message: \"Database connection failed\"\n            }, {\n                status: 503\n            });\n        }\n        const blogId = request.nextUrl.searchParams.get(\"id\");\n        if (blogId) {\n            const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(blogId);\n            return NextResponse.json(blog);\n        } else {\n            const blogs = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].find({});\n            return NextResponse.json({\n                blogs\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in GET /api/blog:\", error.message);\n        return NextResponse.json({\n            success: false,\n            message: \"Database connection error. Please try again later.\",\n            blogs: []\n        }, {\n            status: 503\n        });\n    }\n}\n// API Endpoint For Uploading Blogs\nasync function POST(request) {\n    const formData = await request.formData();\n    const timestamp = Date.now();\n    const image = formData.get(\"image\");\n    const imageByteData = await image.arrayBuffer();\n    const buffer = Buffer.from(imageByteData);\n    const path = `./public/${timestamp}_${image.name}`;\n    await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.writeFile)(path, buffer);\n    const imgUrl = `/${timestamp}_${image.name}`;\n    const blogData = {\n        title: `${formData.get(\"title\")}`,\n        description: `${formData.get(\"description\")}`,\n        category: `${formData.get(\"category\")}`,\n        author: `${formData.get(\"author\")}`,\n        image: `${imgUrl}`,\n        authorImg: `${formData.get(\"authorImg\")}`\n    };\n    const newBlog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create(blogData);\n    console.log(\"Blog Saved\");\n    // Transfer temporary images to the new blog\n    const tempBlogId = formData.get(\"tempBlogId\");\n    if (tempBlogId) {\n        try {\n            await _lib_models_ImageModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].updateMany({\n                blogId: tempBlogId\n            }, {\n                blogId: newBlog._id.toString()\n            });\n            console.log(`Transferred images from ${tempBlogId} to ${newBlog._id}`);\n        } catch (error) {\n            console.error(\"Error transferring images:\", error);\n        }\n    }\n    return NextResponse.json({\n        success: true,\n        msg: \"Blog Added\"\n    });\n}\n// Creating API Endpoint to delete Blog\nasync function DELETE(request) {\n    try {\n        const id = await request.nextUrl.searchParams.get(\"id\");\n        const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(id);\n        if (!blog) {\n            return NextResponse.json({\n                success: false,\n                msg: \"Blog not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Delete the image file if it exists\n        if (blog.image) {\n            fs.unlink(`./public${blog.image}`, ()=>{});\n        }\n        await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findByIdAndDelete(id);\n        return NextResponse.json({\n            success: true,\n            msg: \"Blog Deleted\"\n        });\n    } catch (error) {\n        console.error(\"Error in DELETE /api/blog:\", error.message);\n        return NextResponse.json({\n            success: false,\n            msg: \"Database connection error. Please try again later.\"\n        }, {\n            status: 503\n        });\n    }\n}\n// API Endpoint For Updating Blogs\nasync function PUT(request) {\n    try {\n        const formData = await request.formData();\n        const blogId = formData.get(\"id\");\n        // Find the blog to update\n        const blog = await _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_1__[\"default\"].findById(blogId);\n        if (!blog) {\n            return NextResponse.json({\n                success: false,\n                msg: \"Blog not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Update blog data\n        blog.title = formData.get(\"title\");\n        blog.description = formData.get(\"description\");\n        blog.category = formData.get(\"category\");\n        blog.author = formData.get(\"author\");\n        blog.authorImg = formData.get(\"authorImg\");\n        // Check if a new image was uploaded\n        const image = formData.get(\"image\");\n        if (image && image.name) {\n            // Process new image\n            const imageByteData = await image.arrayBuffer();\n            const buffer = Buffer.from(imageByteData);\n            const timestamp = Date.now();\n            const path = `./public/${timestamp}_${image.name}`;\n            await (0,fs_promises__WEBPACK_IMPORTED_MODULE_3__.writeFile)(path, buffer);\n            const imgUrl = `/${timestamp}_${image.name}`;\n            // Delete old image if it exists\n            if (blog.image) {\n                try {\n                    fs.unlink(`./public${blog.image}`, ()=>{});\n                } catch (error) {\n                    console.error(\"Error deleting old image:\", error);\n                }\n            }\n            // Update image URL\n            blog.image = imgUrl;\n        }\n        // Save updated blog\n        await blog.save();\n        return NextResponse.json({\n            success: true,\n            msg: \"Blog Updated Successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating blog:\", error);\n        return NextResponse.json({\n            success: false,\n            msg: \"Error updating blog\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/blog/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\n// Global variable to track connection state\nlet isConnected = false;\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if (isConnected && (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState === 1) {\n            console.log(\"DB Already Connected\");\n            return true;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        // Improved connection options\n        const options = {\n            serverSelectionTimeoutMS: 30000,\n            socketTimeoutMS: 60000,\n            connectTimeoutMS: 30000,\n            maxPoolSize: 10,\n            minPoolSize: 2,\n            maxIdleTimeMS: 30000,\n            bufferCommands: false,\n            bufferMaxEntries: 0\n        };\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, options);\n        isConnected = true;\n        console.log(\"DB Connected Successfully\");\n        // Handle connection events\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"disconnected\", ()=>{\n            console.log(\"MongoDB disconnected\");\n            isConnected = false;\n        });\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"error\", (err)=>{\n            console.error(\"MongoDB connection error:\", err);\n            isConnected = false;\n        });\n        return true;\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n        isConnected = false;\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/BlogModel.js":
/*!*********************************!*\
  !*** ./lib/models/BlogModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: {\n        type: String,\n        required: true\n    },\n    category: {\n        type: String,\n        required: true\n    },\n    author: {\n        type: String,\n        required: true\n    },\n    image: {\n        type: String,\n        required: true\n    },\n    authorImg: {\n        type: String,\n        required: true\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst BlogModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).blog || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"blog\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0Jsb2dNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsU0FBUyxJQUFJRCx3REFBZSxDQUFDO0lBQy9CRSxPQUFNO1FBQ0ZDLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBQyxhQUFZO1FBQ1JILE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRSxVQUFTO1FBQ0xKLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRyxRQUFPO1FBQ0hMLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSSxPQUFNO1FBQ0ZOLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSyxXQUFVO1FBQ05QLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBTSxNQUFLO1FBQ0RSLE1BQUtTO1FBQ0xDLFNBQVFELEtBQUtFLEdBQUc7SUFDcEI7QUFDSjtBQUVBLE1BQU1DLFlBQVlmLHdEQUFlLENBQUNpQixJQUFJLElBQUlqQixxREFBYyxDQUFDLFFBQU9DO0FBRWhFLGlFQUFlYyxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQmxvZ01vZGVsLmpzPzhmZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcbiAgICB0aXRsZTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBkZXNjcmlwdGlvbjp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBjYXRlZ29yeTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBhdXRob3I6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgaW1hZ2U6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgYXV0aG9ySW1nOntcbiAgICAgICAgdHlwZTpTdHJpbmcsXG4gICAgICAgIHJlcXVpcmVkOnRydWVcbiAgICB9LFxuICAgIGRhdGU6e1xuICAgICAgICB0eXBlOkRhdGUsXG4gICAgICAgIGRlZmF1bHQ6RGF0ZS5ub3coKVxuICAgIH1cbn0pXG5cbmNvbnN0IEJsb2dNb2RlbCA9IG1vbmdvb3NlLm1vZGVscy5ibG9nIHx8IG1vbmdvb3NlLm1vZGVsKCdibG9nJyxTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBCbG9nTW9kZWw7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiU2NoZW1hIiwidGl0bGUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiYXV0aG9yIiwiaW1hZ2UiLCJhdXRob3JJbWciLCJkYXRlIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJCbG9nTW9kZWwiLCJtb2RlbHMiLCJibG9nIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/BlogModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/ImageModel.js":
/*!**********************************!*\
  !*** ./lib/models/ImageModel.js ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    filename: {\n        type: String,\n        required: true\n    },\n    path: {\n        type: String,\n        required: true\n    },\n    url: {\n        type: String,\n        required: true\n    },\n    contentType: {\n        type: String,\n        required: true\n    },\n    size: {\n        type: Number,\n        required: true\n    },\n    data: {\n        type: Buffer,\n        required: true\n    },\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.Mixed,\n        ref: \"blog\",\n        default: null\n    },\n    uploadDate: {\n        type: Date,\n        default: Date.now\n    }\n});\nconst ImageModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).image || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"image\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ImageModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/ImageModel.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fblog%2Froute&page=%2Fapi%2Fblog%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fblog%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();