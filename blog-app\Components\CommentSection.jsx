'use client'
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import CommentForm from './CommentForm';
import CommentItem from './CommentItem';

const CommentSection = ({ blogId, isLoggedIn, onLoginRequired }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [commentsEnabled, setCommentsEnabled] = useState(true);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Fetch comments
  const fetchComments = async (page = 1) => {
    try {
      setLoading(true);
      const response = await axios.get('/api/comments', {
        params: {
          blogId,
          page,
          limit: pagination.limit
        }
      });

      if (response.data.success) {
        setComments(response.data.comments);
        setPagination(response.data.pagination);
        setCommentsEnabled(true);
      } else {
        if (response.data.message.includes('disabled')) {
          setCommentsEnabled(false);
        }
        console.error('Failed to fetch comments:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      if (error.response?.data?.message?.includes('disabled')) {
        setCommentsEnabled(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle new comment submission
  const handleCommentSubmit = async (content) => {
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.post('/api/comments', {
        blogId,
        content
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        toast.success('Comment posted successfully!');
        // Add new comment to the beginning of the list
        setComments(prev => [response.data.comment, ...prev]);
        setPagination(prev => ({
          ...prev,
          total: prev.total + 1
        }));
      } else {
        toast.error(response.data.message || 'Failed to post comment');
      }
    } catch (error) {
      console.error('Error posting comment:', error);
      if (error.response?.status === 401) {
        toast.error('Please log in to comment');
        onLoginRequired();
      } else {
        toast.error(error.response?.data?.message || 'Failed to post comment');
      }
    }
  };

  // Handle reply submission
  const handleReplySubmit = async (parentCommentId, content) => {
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.post('/api/comments', {
        blogId,
        content,
        parentCommentId
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        toast.success('Reply posted successfully!');
        // Add reply to the parent comment
        setComments(prev => prev.map(comment => {
          if (comment._id === parentCommentId) {
            return {
              ...comment,
              replies: [...comment.replies, response.data.comment]
            };
          }
          return comment;
        }));
      } else {
        toast.error(response.data.message || 'Failed to post reply');
      }
    } catch (error) {
      console.error('Error posting reply:', error);
      if (error.response?.status === 401) {
        toast.error('Please log in to reply');
        onLoginRequired();
      } else {
        toast.error(error.response?.data?.message || 'Failed to post reply');
      }
    }
  };

  // Handle comment reaction
  const handleReaction = async (commentId, reactionType) => {
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.post(`/api/comments/${commentId}/react`, {
        reactionType
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        // Update reaction counts in the comments
        const updateCommentReactions = (comments) => {
          return comments.map(comment => {
            if (comment._id === commentId) {
              return {
                ...comment,
                likeCount: response.data.likeCount,
                dislikeCount: response.data.dislikeCount,
                userReaction: response.data.reaction
              };
            }
            // Also check replies
            if (comment.replies && comment.replies.length > 0) {
              return {
                ...comment,
                replies: updateCommentReactions(comment.replies)
              };
            }
            return comment;
          });
        };

        setComments(prev => updateCommentReactions(prev));
      } else {
        toast.error(response.data.message || 'Failed to update reaction');
      }
    } catch (error) {
      console.error('Error updating reaction:', error);
      if (error.response?.status === 401) {
        toast.error('Please log in to react');
        onLoginRequired();
      } else {
        toast.error(error.response?.data?.message || 'Failed to update reaction');
      }
    }
  };

  // Load more comments
  const loadMoreComments = () => {
    if (pagination.page < pagination.pages) {
      fetchComments(pagination.page + 1);
    }
  };

  useEffect(() => {
    if (blogId) {
      fetchComments();
    }
  }, [blogId]);

  if (!commentsEnabled) {
    return (
      <div className="max-w-4xl mx-auto mt-12 p-6 bg-gray-50 rounded-lg">
        <h3 className="text-xl font-semibold mb-4">Comments</h3>
        <p className="text-gray-600">Comments are currently disabled for this blog.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto mt-12 p-6">
      <h3 className="text-2xl font-semibold mb-6">
        Comments ({pagination.total})
      </h3>

      {/* Comment Form */}
      <div className="mb-8">
        <CommentForm 
          onSubmit={handleCommentSubmit}
          isLoggedIn={isLoggedIn}
          onLoginRequired={onLoginRequired}
          placeholder="Share your thoughts about this blog post..."
        />
      </div>

      {/* Comments List */}
      {loading ? (
        <div className="text-center py-8">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          <p className="mt-2 text-gray-600">Loading comments...</p>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <p className="text-gray-600">No comments yet. Be the first to share your thoughts!</p>
        </div>
      ) : (
        <>
          <div className="space-y-6">
            {comments.map((comment) => (
              <CommentItem
                key={comment._id}
                comment={comment}
                onReply={handleReplySubmit}
                onReaction={handleReaction}
                isLoggedIn={isLoggedIn}
                onLoginRequired={onLoginRequired}
              />
            ))}
          </div>

          {/* Load More Button */}
          {pagination.page < pagination.pages && (
            <div className="text-center mt-8">
              <button
                onClick={loadMoreComments}
                className="px-6 py-2 bg-black text-white border border-black hover:bg-gray-800 transition-colors"
              >
                Load More Comments
              </button>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default CommentSection;
