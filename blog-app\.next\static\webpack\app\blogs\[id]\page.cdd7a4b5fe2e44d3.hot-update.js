"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[id]/page",{

/***/ "(app-pages-browser)/./Components/CommentSection.jsx":
/*!***************************************!*\
  !*** ./Components/CommentSection.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _CommentForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CommentForm */ \"(app-pages-browser)/./Components/CommentForm.jsx\");\n/* harmony import */ var _CommentItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CommentItem */ \"(app-pages-browser)/./Components/CommentItem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CommentSection = (param)=>{\n    let { blogId, isLoggedIn, onLoginRequired } = param;\n    _s();\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [commentsEnabled, setCommentsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [commentsLoaded, setCommentsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 10,\n        total: 0,\n        pages: 0\n    });\n    // Fetch comments\n    const fetchComments = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/api/comments\", {\n                params: {\n                    blogId,\n                    page,\n                    limit: pagination.limit\n                }\n            });\n            if (response.data.success) {\n                setComments(response.data.comments);\n                setPagination(response.data.pagination);\n                setCommentsEnabled(true);\n                setCommentsLoaded(true);\n            } else {\n                if (response.data.message.includes(\"disabled\")) {\n                    setCommentsEnabled(false);\n                }\n                console.error(\"Failed to fetch comments:\", response.data.message);\n            }\n        } catch (error) {\n            var _error_response_data_message, _error_response_data, _error_response;\n            console.error(\"Error fetching comments:\", error);\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_message = _error_response_data.message) === null || _error_response_data_message === void 0 ? void 0 : _error_response_data_message.includes(\"disabled\")) {\n                setCommentsEnabled(false);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Toggle comments visibility\n    const toggleComments = ()=>{\n        setShowComments(!showComments);\n        // Only fetch comments when showing them for the first time\n        if (!showComments && !commentsLoaded) {\n            fetchComments();\n        }\n    };\n    // Handle new comment submission\n    const handleCommentSubmit = async (content)=>{\n        if (!isLoggedIn) {\n            onLoginRequired();\n            return;\n        }\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"/api/comments\", {\n                blogId,\n                content\n            }, {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Comment posted successfully!\");\n                // Add new comment to the beginning of the list\n                setComments((prev)=>[\n                        response.data.comment,\n                        ...prev\n                    ]);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: prev.total + 1\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(response.data.message || \"Failed to post comment\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error posting comment:\", error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please log in to comment\");\n                onLoginRequired();\n            } else {\n                var _error_response_data, _error_response1;\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to post comment\");\n            }\n        }\n    };\n    // Handle reply submission\n    const handleReplySubmit = async (parentCommentId, content)=>{\n        if (!isLoggedIn) {\n            onLoginRequired();\n            return;\n        }\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"/api/comments\", {\n                blogId,\n                content,\n                parentCommentId\n            }, {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Reply posted successfully!\");\n                // Add reply to the parent comment\n                setComments((prev)=>prev.map((comment)=>{\n                        if (comment._id === parentCommentId) {\n                            return {\n                                ...comment,\n                                replies: [\n                                    ...comment.replies,\n                                    response.data.comment\n                                ]\n                            };\n                        }\n                        return comment;\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(response.data.message || \"Failed to post reply\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error posting reply:\", error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please log in to reply\");\n                onLoginRequired();\n            } else {\n                var _error_response_data, _error_response1;\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to post reply\");\n            }\n        }\n    };\n    // Handle comment reaction\n    const handleReaction = async (commentId, reactionType)=>{\n        if (!isLoggedIn) {\n            onLoginRequired();\n            return;\n        }\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"/api/comments/\".concat(commentId, \"/react\"), {\n                reactionType\n            }, {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                // Update reaction counts in the comments\n                const updateCommentReactions = (comments)=>{\n                    return comments.map((comment)=>{\n                        if (comment._id === commentId) {\n                            return {\n                                ...comment,\n                                likeCount: response.data.likeCount,\n                                dislikeCount: response.data.dislikeCount,\n                                userReaction: response.data.reaction\n                            };\n                        }\n                        // Also check replies\n                        if (comment.replies && comment.replies.length > 0) {\n                            return {\n                                ...comment,\n                                replies: updateCommentReactions(comment.replies)\n                            };\n                        }\n                        return comment;\n                    });\n                };\n                setComments((prev)=>updateCommentReactions(prev));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(response.data.message || \"Failed to update reaction\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error updating reaction:\", error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please log in to react\");\n                onLoginRequired();\n            } else {\n                var _error_response_data, _error_response1;\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to update reaction\");\n            }\n        }\n    };\n    // Load more comments\n    const loadMoreComments = ()=>{\n        if (pagination.page < pagination.pages) {\n            fetchComments(pagination.page + 1);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (blogId) {\n            fetchComments();\n        }\n    }, [\n        blogId\n    ]);\n    if (!commentsEnabled) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto mt-12 p-6 bg-gray-50 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xl font-semibold mb-4\",\n                    children: \"Comments\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Comments are currently disabled for this blog.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto mt-12 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-6\",\n                children: [\n                    \"Comments (\",\n                    pagination.total,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 227,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSubmit: handleCommentSubmit,\n                    isLoggedIn: isLoggedIn,\n                    onLoginRequired: onLoginRequired,\n                    placeholder: \"Share your thoughts about this blog post...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 244,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading comments...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 243,\n                columnNumber: 9\n            }, undefined) : comments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 bg-gray-50 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"No comments yet. Be the first to share your thoughts!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 249,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 248,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: comments.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                comment: comment,\n                                onReply: handleReplySubmit,\n                                onReaction: handleReaction,\n                                isLoggedIn: isLoggedIn,\n                                onLoginRequired: onLoginRequired\n                            }, comment._id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                                lineNumber: 255,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 253,\n                        columnNumber: 11\n                    }, undefined),\n                    pagination.page < pagination.pages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMoreComments,\n                            className: \"px-6 py-2 bg-black text-white border border-black hover:bg-gray-800 transition-colors\",\n                            children: \"Load More Comments\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                            lineNumber: 269,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 268,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n        lineNumber: 226,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CommentSection, \"AIpv8r40MWhsu0Qv74n2TOZOkds=\");\n_c = CommentSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CommentSection);\nvar _c;\n$RefreshReg$(_c, \"CommentSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/CommentSection.jsx\n"));

/***/ })

});