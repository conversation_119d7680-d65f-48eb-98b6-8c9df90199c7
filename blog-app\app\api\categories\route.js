import { ConnectDB } from "@/lib/config/db";
import { NextResponse } from "next/server";
import CategoryModel from "@/lib/models/CategoryModel";

// Initialize database connection
const LoadDB = async () => {
  await ConnectDB();
}
LoadDB();

// GET - Fetch all categories
export async function GET() {
  try {
    // Ensure we're connected to the database
    const isConnected = await ConnectDB();
    if (!isConnected) {
      return NextResponse.json({
        success: false,
        message: "Database connection failed",
        categories: []
      }, { status: 503 });
    }

    // Fetch all categories sorted by name
    const categories = await CategoryModel.find().sort({ name: 1 });

    // If no categories exist, create default ones
    if (categories.length === 0) {
      const defaultCategories = ['Startup', 'Technology', 'Lifestyle'];

      for (const name of defaultCategories) {
        await CategoryModel.create({ name });
      }

      // Fetch again after creating defaults
      const newCategories = await CategoryModel.find().sort({ name: 1 });
      console.log("Created default categories:", newCategories);

      return NextResponse.json({
        success: true,
        categories: newCategories
      });
    }

    console.log("Fetched categories:", categories);

    return NextResponse.json({
      success: true,
      categories
    });
  } catch (error) {
    console.error("Error fetching categories:", error);
    return NextResponse.json({
      success: false,
      message: "Database connection error. Please try again later.",
      categories: []
    }, { status: 503 });
  }
}

// POST - Create a new category
export async function POST(request) {
  try {
    // Ensure we're connected to the database
    await ConnectDB();
    
    const { name } = await request.json();
    
    if (!name || !name.trim()) {
      return NextResponse.json({
        success: false,
        message: "Category name is required"
      }, { status: 400 });
    }
    
    // Check if category already exists
    const existingCategory = await CategoryModel.findOne({ 
      name: { $regex: new RegExp(`^${name.trim()}$`, 'i') } 
    });
    
    if (existingCategory) {
      return NextResponse.json({
        success: false,
        message: "Category already exists"
      }, { status: 409 });
    }
    
    // Create new category
    const newCategory = await CategoryModel.create({ name: name.trim() });
    
    console.log("New category created:", newCategory);
    
    // Fetch all categories to return the updated list
    const allCategories = await CategoryModel.find().sort({ name: 1 });
    
    return NextResponse.json({
      success: true,
      message: "Category created successfully",
      category: newCategory,
      categories: allCategories
    });
  } catch (error) {
    console.error("Error creating category:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to create category"
    }, { status: 500 });
  }
}

// DELETE - Remove a category
export async function DELETE(request) {
  try {
    // Ensure we're connected to the database
    await ConnectDB();
    
    const id = request.nextUrl.searchParams.get('id');
    
    if (!id) {
      return NextResponse.json({
        success: false,
        message: "Category ID is required"
      }, { status: 400 });
    }
    
    // Check if category exists
    const category = await CategoryModel.findById(id);
    if (!category) {
      return NextResponse.json({
        success: false,
        message: "Category not found"
      }, { status: 404 });
    }
    
    // Delete the category
    await CategoryModel.findByIdAndDelete(id);
    
    // Fetch all categories to return the updated list
    const allCategories = await CategoryModel.find().sort({ name: 1 });
    
    return NextResponse.json({
      success: true,
      message: "Category deleted successfully",
      categories: allCategories
    });
  } catch (error) {
    console.error("Error deleting category:", error);
    return NextResponse.json({
      success: false,
      message: "Failed to delete category"
    }, { status: 500 });
  }
}


