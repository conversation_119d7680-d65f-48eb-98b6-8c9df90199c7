"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/comments/route";
exports.ids = ["app/api/admin/comments/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcomments%2Froute&page=%2Fapi%2Fadmin%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcomments%2Froute&page=%2Fapi%2Fadmin%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_admin_comments_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/admin/comments/route.js */ \"(rsc)/./app/api/admin/comments/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/comments/route\",\n        pathname: \"/api/admin/comments\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/comments/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\admin\\\\comments\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_admin_comments_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/admin/comments/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcomments%2Froute&page=%2Fapi%2Fadmin%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/admin/comments/route.js":
/*!*****************************************!*\
  !*** ./app/api/admin/comments/route.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n/* harmony import */ var _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CommentModel */ \"(rsc)/./lib/models/CommentModel.js\");\n/* harmony import */ var _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/models/CommentReactionModel */ \"(rsc)/./lib/models/CommentReactionModel.js\");\n/* harmony import */ var _lib_models_BlogModel__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/models/BlogModel */ \"(rsc)/./lib/models/BlogModel.js\");\n\n\n\n\n\n\n// Helper function to get user from token\nfunction getUserFromToken(request) {\n    const authHeader = request.headers.get(\"Authorization\");\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    const token = authHeader.split(\" \")[1];\n    return (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n}\n// Helper function to check if user is admin\nfunction isAdmin(userData) {\n    return userData && userData.role === \"admin\";\n}\n// GET - Fetch all comments for admin management\nasync function GET(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const userData = getUserFromToken(request);\n        if (!userData || !isAdmin(userData)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get(\"page\")) || 1;\n        const limit = parseInt(searchParams.get(\"limit\")) || 20;\n        const blogId = searchParams.get(\"blogId\");\n        const sortBy = searchParams.get(\"sortBy\") || \"createdAt\";\n        const sortOrder = searchParams.get(\"sortOrder\") || \"desc\";\n        const skip = (page - 1) * limit;\n        // Build query\n        const query = {\n            isDeleted: false\n        };\n        if (blogId) {\n            query.blogId = blogId;\n        }\n        // Build sort object\n        const sort = {};\n        sort[sortBy] = sortOrder === \"desc\" ? -1 : 1;\n        // Fetch comments with user and blog info\n        const comments = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].find(query).populate(\"userId\", \"name email profilePicture\").populate(\"blogId\", \"title\").populate(\"parentCommentId\", \"content userId\").sort(sort).skip(skip).limit(limit);\n        // Add reaction counts to each comment\n        const commentsWithDetails = await Promise.all(comments.map(async (comment)=>{\n            const likeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                commentId: comment._id,\n                reactionType: \"like\"\n            });\n            const dislikeCount = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments({\n                commentId: comment._id,\n                reactionType: \"dislike\"\n            });\n            // Count replies if this is a top-level comment\n            const replyCount = comment.parentCommentId ? 0 : await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments({\n                parentCommentId: comment._id,\n                isDeleted: false\n            });\n            return {\n                ...comment.toObject(),\n                likeCount,\n                dislikeCount,\n                replyCount\n            };\n        }));\n        // Get total count for pagination\n        const totalComments = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments(query);\n        // Get summary statistics\n        const totalAllComments = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments({\n            isDeleted: false\n        });\n        const totalDeletedComments = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].countDocuments({\n            isDeleted: true\n        });\n        const totalReactions = await _lib_models_CommentReactionModel__WEBPACK_IMPORTED_MODULE_4__[\"default\"].countDocuments();\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            comments: commentsWithDetails,\n            pagination: {\n                page,\n                limit,\n                total: totalComments,\n                pages: Math.ceil(totalComments / limit)\n            },\n            statistics: {\n                totalComments: totalAllComments,\n                totalDeletedComments,\n                totalReactions\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching admin comments:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch comments\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Delete a comment (Admin only)\nasync function DELETE(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const userData = getUserFromToken(request);\n        if (!userData || !isAdmin(userData)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const commentId = searchParams.get(\"commentId\");\n        if (!commentId) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Comment ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Find the comment\n        const comment = await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findById(commentId);\n        if (!comment) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Comment not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Mark comment as deleted (soft delete)\n        comment.isDeleted = true;\n        await comment.save();\n        // Also mark all replies as deleted\n        await _lib_models_CommentModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].updateMany({\n            parentCommentId: commentId\n        }, {\n            isDeleted: true\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Comment and its replies deleted successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error deleting comment:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to delete comment\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/admin/comments/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/BlogModel.js":
/*!*********************************!*\
  !*** ./lib/models/BlogModel.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Schema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    title: {\n        type: String,\n        required: true\n    },\n    description: {\n        type: String,\n        required: true\n    },\n    category: {\n        type: String,\n        required: true\n    },\n    author: {\n        type: String,\n        required: true\n    },\n    image: {\n        type: String,\n        required: true\n    },\n    authorImg: {\n        type: String,\n        required: true\n    },\n    date: {\n        type: Date,\n        default: Date.now()\n    }\n});\nconst BlogModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).blog || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"blog\", Schema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (BlogModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0Jsb2dNb2RlbC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBZ0M7QUFFaEMsTUFBTUMsU0FBUyxJQUFJRCx3REFBZSxDQUFDO0lBQy9CRSxPQUFNO1FBQ0ZDLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBQyxhQUFZO1FBQ1JILE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRSxVQUFTO1FBQ0xKLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBRyxRQUFPO1FBQ0hMLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSSxPQUFNO1FBQ0ZOLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBSyxXQUFVO1FBQ05QLE1BQUtDO1FBQ0xDLFVBQVM7SUFDYjtJQUNBTSxNQUFLO1FBQ0RSLE1BQUtTO1FBQ0xDLFNBQVFELEtBQUtFLEdBQUc7SUFDcEI7QUFDSjtBQUVBLE1BQU1DLFlBQVlmLHdEQUFlLENBQUNpQixJQUFJLElBQUlqQixxREFBYyxDQUFDLFFBQU9DO0FBRWhFLGlFQUFlYyxTQUFTQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQmxvZ01vZGVsLmpzPzhmZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IG1vbmdvb3NlIGZyb20gXCJtb25nb29zZVwiO1xuXG5jb25zdCBTY2hlbWEgPSBuZXcgbW9uZ29vc2UuU2NoZW1hKHtcbiAgICB0aXRsZTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBkZXNjcmlwdGlvbjp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBjYXRlZ29yeTp7XG4gICAgICAgIHR5cGU6U3RyaW5nLFxuICAgICAgICByZXF1aXJlZDp0cnVlXG4gICAgfSxcbiAgICBhdXRob3I6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgaW1hZ2U6e1xuICAgICAgICB0eXBlOlN0cmluZyxcbiAgICAgICAgcmVxdWlyZWQ6dHJ1ZVxuICAgIH0sXG4gICAgYXV0aG9ySW1nOntcbiAgICAgICAgdHlwZTpTdHJpbmcsXG4gICAgICAgIHJlcXVpcmVkOnRydWVcbiAgICB9LFxuICAgIGRhdGU6e1xuICAgICAgICB0eXBlOkRhdGUsXG4gICAgICAgIGRlZmF1bHQ6RGF0ZS5ub3coKVxuICAgIH1cbn0pXG5cbmNvbnN0IEJsb2dNb2RlbCA9IG1vbmdvb3NlLm1vZGVscy5ibG9nIHx8IG1vbmdvb3NlLm1vZGVsKCdibG9nJyxTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBCbG9nTW9kZWw7Il0sIm5hbWVzIjpbIm1vbmdvb3NlIiwiU2NoZW1hIiwidGl0bGUiLCJ0eXBlIiwiU3RyaW5nIiwicmVxdWlyZWQiLCJkZXNjcmlwdGlvbiIsImNhdGVnb3J5IiwiYXV0aG9yIiwiaW1hZ2UiLCJhdXRob3JJbWciLCJkYXRlIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJCbG9nTW9kZWwiLCJtb2RlbHMiLCJibG9nIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/BlogModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentModel.js":
/*!************************************!*\
  !*** ./lib/models/CommentModel.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        required: true,\n        index: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true,\n        index: true\n    },\n    content: {\n        type: String,\n        required: true,\n        trim: true,\n        maxlength: 1000\n    },\n    parentCommentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Comment\",\n        default: null,\n        index: true\n    },\n    isDeleted: {\n        type: Boolean,\n        default: false\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now,\n        index: true\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Update the updatedAt field before saving\nCommentSchema.pre(\"save\", function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Virtual for getting replies\nCommentSchema.virtual(\"replies\", {\n    ref: \"Comment\",\n    localField: \"_id\",\n    foreignField: \"parentCommentId\"\n});\n// Virtual for getting like count\nCommentSchema.virtual(\"likeCount\", {\n    ref: \"CommentReaction\",\n    localField: \"_id\",\n    foreignField: \"commentId\",\n    count: true,\n    match: {\n        reactionType: \"like\"\n    }\n});\n// Virtual for getting dislike count\nCommentSchema.virtual(\"dislikeCount\", {\n    ref: \"CommentReaction\",\n    localField: \"_id\",\n    foreignField: \"commentId\",\n    count: true,\n    match: {\n        reactionType: \"dislike\"\n    }\n});\n// Ensure virtual fields are serialized\nCommentSchema.set(\"toJSON\", {\n    virtuals: true\n});\nCommentSchema.set(\"toObject\", {\n    virtuals: true\n});\nconst CommentModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Comment || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Comment\", CommentSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentReactionModel.js":
/*!********************************************!*\
  !*** ./lib/models/CommentReactionModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentReactionSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    commentId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"Comment\",\n        required: true,\n        index: true\n    },\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true,\n        index: true\n    },\n    reactionType: {\n        type: String,\n        required: true,\n        enum: [\n            \"like\",\n            \"dislike\"\n        ],\n        index: true\n    },\n    createdAt: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Create compound index to ensure a user can only have one reaction per comment\nCommentReactionSchema.index({\n    commentId: 1,\n    userId: 1\n}, {\n    unique: true\n});\nconst CommentReactionModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CommentReaction || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"CommentReaction\", CommentReactionSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentReactionModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentReactionModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fadmin%2Fcomments%2Froute&page=%2Fapi%2Fadmin%2Fcomments%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcomments%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();