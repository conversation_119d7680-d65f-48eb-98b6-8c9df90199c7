"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/categories/route";
exports.ids = ["app/api/categories/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_categories_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/categories/route.js */ \"(rsc)/./app/api/categories/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/categories/route\",\n        pathname: \"/api/categories\",\n        filename: \"route\",\n        bundlePath: \"app/api/categories/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\categories\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_categories_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/categories/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/categories/route.js":
/*!*************************************!*\
  !*** ./app/api/categories/route.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/CategoryModel */ \"(rsc)/./lib/models/CategoryModel.js\");\n\n\n\n// Initialize database connection\nconst LoadDB = async ()=>{\n    await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n};\nLoadDB();\n// GET - Fetch all categories\nasync function GET() {\n    try {\n        // Ensure we're connected to the database\n        const isConnected = await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n        if (!isConnected) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                message: \"Database connection failed\",\n                categories: []\n            }, {\n                status: 503\n            });\n        }\n        // Fetch all categories sorted by name\n        const categories = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find().sort({\n            name: 1\n        });\n        // If no categories exist, create default ones\n        if (categories.length === 0) {\n            const defaultCategories = [\n                \"Startup\",\n                \"Technology\",\n                \"Lifestyle\"\n            ];\n            for (const name of defaultCategories){\n                await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n                    name\n                });\n            }\n            // Fetch again after creating defaults\n            const newCategories = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find().sort({\n                name: 1\n            });\n            console.log(\"Created default categories:\", newCategories);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: true,\n                categories: newCategories\n            });\n        }\n        console.log(\"Fetched categories:\", categories);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            categories\n        });\n    } catch (error) {\n        console.error(\"Error fetching categories:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            message: \"Database connection error. Please try again later.\",\n            categories: []\n        }, {\n            status: 503\n        });\n    }\n}\n// POST - Create a new category\nasync function POST(request) {\n    try {\n        // Ensure we're connected to the database\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n        const { name } = await request.json();\n        if (!name || !name.trim()) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                message: \"Category name is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if category already exists\n        const existingCategory = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findOne({\n            name: {\n                $regex: new RegExp(`^${name.trim()}$`, \"i\")\n            }\n        });\n        if (existingCategory) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                message: \"Category already exists\"\n            }, {\n                status: 409\n            });\n        }\n        // Create new category\n        const newCategory = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            name: name.trim()\n        });\n        console.log(\"New category created:\", newCategory);\n        // Fetch all categories to return the updated list\n        const allCategories = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find().sort({\n            name: 1\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            message: \"Category created successfully\",\n            category: newCategory,\n            categories: allCategories\n        });\n    } catch (error) {\n        console.error(\"Error creating category:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            message: \"Failed to create category\"\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE - Remove a category\nasync function DELETE(request) {\n    try {\n        // Ensure we're connected to the database\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_0__.ConnectDB)();\n        const id = request.nextUrl.searchParams.get(\"id\");\n        if (!id) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                message: \"Category ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Check if category exists\n        const category = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findById(id);\n        if (!category) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n                success: false,\n                message: \"Category not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Delete the category\n        await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].findByIdAndDelete(id);\n        // Fetch all categories to return the updated list\n        const allCategories = await _lib_models_CategoryModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].find().sort({\n            name: 1\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: true,\n            message: \"Category deleted successfully\",\n            categories: allCategories\n        });\n    } catch (error) {\n        console.error(\"Error deleting category:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_1__[\"default\"].json({\n            success: false,\n            message: \"Failed to delete category\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/categories/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\n// Global variable to track connection state\nlet isConnected = false;\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if (isConnected && (mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState === 1) {\n            console.log(\"DB Already Connected\");\n            return true;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        // Improved connection options\n        const options = {\n            serverSelectionTimeoutMS: 30000,\n            socketTimeoutMS: 60000,\n            connectTimeoutMS: 30000,\n            maxPoolSize: 10,\n            minPoolSize: 2,\n            maxIdleTimeMS: 30000,\n            bufferCommands: false,\n            bufferMaxEntries: 0\n        };\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, options);\n        isConnected = true;\n        console.log(\"DB Connected Successfully\");\n        // Handle connection events\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"disconnected\", ()=>{\n            console.log(\"MongoDB disconnected\");\n            isConnected = false;\n        });\n        mongoose__WEBPACK_IMPORTED_MODULE_0___default().connection.on(\"error\", (err)=>{\n            console.error(\"MongoDB connection error:\", err);\n            isConnected = false;\n        });\n        return true;\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n        isConnected = false;\n        return false;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CategoryModel.js":
/*!*************************************!*\
  !*** ./lib/models/CategoryModel.js ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\n// Define Category Schema\nconst CategorySchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    name: {\n        type: String,\n        required: true,\n        unique: true,\n        trim: true\n    },\n    date: {\n        type: Date,\n        default: Date.now\n    }\n});\n// Check if the model already exists to prevent overwriting\nconst CategoryModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).Category || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"Category\", CategorySchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CategoryModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0NhdGVnb3J5TW9kZWwuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBRWhDLHlCQUF5QjtBQUN6QixNQUFNQyxpQkFBaUIsSUFBSUQsd0RBQWUsQ0FBQztJQUN6Q0csTUFBTTtRQUNKQyxNQUFNQztRQUNOQyxVQUFVO1FBQ1ZDLFFBQVE7UUFDUkMsTUFBTTtJQUNSO0lBQ0FDLE1BQU07UUFDSkwsTUFBTU07UUFDTkMsU0FBU0QsS0FBS0UsR0FBRztJQUNuQjtBQUNGO0FBRUEsMkRBQTJEO0FBQzNELE1BQU1DLGdCQUFnQmIsd0RBQWUsQ0FBQ2UsUUFBUSxJQUFJZixxREFBYyxDQUFDLFlBQVlDO0FBRTdFLGlFQUFlWSxhQUFhQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQ2F0ZWdvcnlNb2RlbC5qcz84NTFhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBtb25nb29zZSBmcm9tICdtb25nb29zZSc7XG5cbi8vIERlZmluZSBDYXRlZ29yeSBTY2hlbWFcbmNvbnN0IENhdGVnb3J5U2NoZW1hID0gbmV3IG1vbmdvb3NlLlNjaGVtYSh7XG4gIG5hbWU6IHtcbiAgICB0eXBlOiBTdHJpbmcsXG4gICAgcmVxdWlyZWQ6IHRydWUsXG4gICAgdW5pcXVlOiB0cnVlLFxuICAgIHRyaW06IHRydWVcbiAgfSxcbiAgZGF0ZToge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3dcbiAgfVxufSk7XG5cbi8vIENoZWNrIGlmIHRoZSBtb2RlbCBhbHJlYWR5IGV4aXN0cyB0byBwcmV2ZW50IG92ZXJ3cml0aW5nXG5jb25zdCBDYXRlZ29yeU1vZGVsID0gbW9uZ29vc2UubW9kZWxzLkNhdGVnb3J5IHx8IG1vbmdvb3NlLm1vZGVsKCdDYXRlZ29yeScsIENhdGVnb3J5U2NoZW1hKTtcblxuZXhwb3J0IGRlZmF1bHQgQ2F0ZWdvcnlNb2RlbDtcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIkNhdGVnb3J5U2NoZW1hIiwiU2NoZW1hIiwibmFtZSIsInR5cGUiLCJTdHJpbmciLCJyZXF1aXJlZCIsInVuaXF1ZSIsInRyaW0iLCJkYXRlIiwiRGF0ZSIsImRlZmF1bHQiLCJub3ciLCJDYXRlZ29yeU1vZGVsIiwibW9kZWxzIiwiQ2F0ZWdvcnkiLCJtb2RlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CategoryModel.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcategories%2Froute&page=%2Fapi%2Fcategories%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcategories%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();