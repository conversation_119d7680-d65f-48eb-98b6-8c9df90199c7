/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/profile/page";
exports.ids = ["app/profile/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'profile',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.jsx */ \"(rsc)/./app/profile/page.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.jsx */ \"(rsc)/./app/layout.jsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/profile/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/profile/page\",\n        pathname: \"/profile\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CEmailSubscriptionPopup.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CEmailSubscriptionPopup.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./Components/CookieConsent.jsx */ \"(ssr)/./Components/CookieConsent.jsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./Components/EmailSubscriptionPopup.jsx */ \"(ssr)/./Components/EmailSubscriptionPopup.jsx\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-toastify/dist/react-toastify.esm.mjs */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CCookieConsent.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5CComponents%5CEmailSubscriptionPopup.jsx&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22app%5C%5Clayout.jsx%22%2C%22import%22%3A%22Outfit%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22weight%22%3A%5B%22400%22%2C%22500%22%2C%22600%22%2C%22700%22%5D%7D%5D%2C%22variableName%22%3A%22outfit%22%7D&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cglobals.css&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5Creact-toastify.esm.mjs&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Creact-toastify%5Cdist%5CReactToastify.css&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cprofile%5Cpage.jsx&server=true!":
/*!********************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cprofile%5Cpage.jsx&server=true! ***!
  \********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/profile/page.jsx */ \"(ssr)/./app/profile/page.jsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9QyUzQSU1Q1VzZXJzJTVDRFNZUyU1Q0Rlc2t0b3AlNUNNci5CbG9nJTVDYmxvZy1hcHAlNUNhcHAlNUNwcm9maWxlJTVDcGFnZS5qc3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8/YmJmYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXERTWVNcXFxcRGVza3RvcFxcXFxNci5CbG9nXFxcXGJsb2ctYXBwXFxcXGFwcFxcXFxwcm9maWxlXFxcXHBhZ2UuanN4XCIpIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp%5Cprofile%5Cpage.jsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./Assets/assets.js":
/*!**************************!*\
  !*** ./Assets/assets.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assets: () => (/* binding */ assets),\n/* harmony export */   blog_data: () => (/* binding */ blog_data)\n/* harmony export */ });\n/* harmony import */ var _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./blog_pic_1.png */ \"(ssr)/./Assets/blog_pic_1.png\");\n/* harmony import */ var _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./blog_pic_2.png */ \"(ssr)/./Assets/blog_pic_2.png\");\n/* harmony import */ var _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./blog_pic_3.png */ \"(ssr)/./Assets/blog_pic_3.png\");\n/* harmony import */ var _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog_pic_4.png */ \"(ssr)/./Assets/blog_pic_4.png\");\n/* harmony import */ var _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./blog_pic_5.png */ \"(ssr)/./Assets/blog_pic_5.png\");\n/* harmony import */ var _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./blog_pic_6.png */ \"(ssr)/./Assets/blog_pic_6.png\");\n/* harmony import */ var _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./blog_pic_7.png */ \"(ssr)/./Assets/blog_pic_7.png\");\n/* harmony import */ var _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./blog_pic_8.png */ \"(ssr)/./Assets/blog_pic_8.png\");\n/* harmony import */ var _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./blog_pic_9.png */ \"(ssr)/./Assets/blog_pic_9.png\");\n/* harmony import */ var _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./blog_pic_10.png */ \"(ssr)/./Assets/blog_pic_10.png\");\n/* harmony import */ var _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./blog_pic_11.png */ \"(ssr)/./Assets/blog_pic_11.png\");\n/* harmony import */ var _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./blog_pic_12.png */ \"(ssr)/./Assets/blog_pic_12.png\");\n/* harmony import */ var _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./blog_pic_13.png */ \"(ssr)/./Assets/blog_pic_13.png\");\n/* harmony import */ var _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./blog_pic_14.png */ \"(ssr)/./Assets/blog_pic_14.png\");\n/* harmony import */ var _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./blog_pic_15.png */ \"(ssr)/./Assets/blog_pic_15.png\");\n/* harmony import */ var _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./blog_pic_16.png */ \"(ssr)/./Assets/blog_pic_16.png\");\n/* harmony import */ var _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./facebook_icon.png */ \"(ssr)/./Assets/facebook_icon.png\");\n/* harmony import */ var _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./googleplus_icon.png */ \"(ssr)/./Assets/googleplus_icon.png\");\n/* harmony import */ var _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./twitter_icon.png */ \"(ssr)/./Assets/twitter_icon.png\");\n/* harmony import */ var _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./profile_icon.png */ \"(ssr)/./Assets/profile_icon.png\");\n/* harmony import */ var _logo_png__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./logo.png */ \"(ssr)/./Assets/logo.png\");\n/* harmony import */ var _arrow_png__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./arrow.png */ \"(ssr)/./Assets/arrow.png\");\n/* harmony import */ var _logo_light_png__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! ./logo_light.png */ \"(ssr)/./Assets/logo_light.png\");\n/* harmony import */ var _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! ./blog_icon.png */ \"(ssr)/./Assets/blog_icon.png\");\n/* harmony import */ var _add_icon_png__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./add_icon.png */ \"(ssr)/./Assets/add_icon.png\");\n/* harmony import */ var _email_icon_png__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! ./email_icon.png */ \"(ssr)/./Assets/email_icon.png\");\n/* harmony import */ var _upload_area_png__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! ./upload_area.png */ \"(ssr)/./Assets/upload_area.png\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst assets = {\n    facebook_icon: _facebook_icon_png__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n    googleplus_icon: _googleplus_icon_png__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    twitter_icon: _twitter_icon_png__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    profile_icon: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    logo: _logo_png__WEBPACK_IMPORTED_MODULE_20__[\"default\"],\n    arrow: _arrow_png__WEBPACK_IMPORTED_MODULE_21__[\"default\"],\n    logo_light: _logo_light_png__WEBPACK_IMPORTED_MODULE_22__[\"default\"],\n    blog_icon: _blog_icon_png__WEBPACK_IMPORTED_MODULE_23__[\"default\"],\n    add_icon: _add_icon_png__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n    email_icon: _email_icon_png__WEBPACK_IMPORTED_MODULE_25__[\"default\"],\n    upload_area: _upload_area_png__WEBPACK_IMPORTED_MODULE_26__[\"default\"]\n};\nconst blog_data = [\n    {\n        id: 1,\n        title: \"A detailed step by step guide to manage your lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_1_png__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 2,\n        title: \"How to create an effective startup roadmap or ideas\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_2_png__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 3,\n        title: \"Learning new technology to boost your career in software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_3_png__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 4,\n        title: \"Tips for getting the most out of apps and software\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_4_png__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 5,\n        title: \"Enhancing your skills and capturing memorable moments\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_5_png__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 6,\n        title: \"Maximizing returns by minimizing resources in your startup\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_6_png__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 7,\n        title: \"Technology for Career advancement in development\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_7_png__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 8,\n        title: \"A comprehensive roadmap for effective lifestyle management\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_8_png__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 9,\n        title: \"Achieving maximum returns with minimal resources\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_9_png__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 10,\n        title: \"Beyond the Ordinary: Crafting Your Exceptional Lifestyle\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_10_png__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 11,\n        title: \"Unveiling the Secrets of Successful Startups in Technolgy\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_11_png__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 12,\n        title: \"How to design an online Learning Platform today\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_12_png__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 13,\n        title: \"Tomorrow's Algorithms: Shaping the Landscape of Future AI\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_13_png__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 14,\n        title: \"Balance & Bliss: Navigating Life's Journey with Style\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_14_png__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        date: Date.now(),\n        category: \"Lifestyle\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 15,\n        title: \"Exploring the Evolution of social networking in the Future\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_15_png__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        date: Date.now(),\n        category: \"Technology\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    },\n    {\n        id: 16,\n        title: \"Shaping the Future of statup ecosystem in the world\",\n        description: \"Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the..\",\n        image: _blog_pic_16_png__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        date: Date.now(),\n        category: \"Startup\",\n        author: \"Alex Bennett\",\n        author_img: _profile_icon_png__WEBPACK_IMPORTED_MODULE_19__[\"default\"]\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXNzZXRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0c7QUFDSTtBQUNOO0FBQ0E7QUFDaEI7QUFDRTtBQUNVO0FBQ0Y7QUFDRjtBQUNJO0FBQ0U7QUFFcEMsTUFBTTJCLFNBQVM7SUFDbEJYLGFBQWFBLDZEQUFBQTtJQUNiQyxlQUFlQSwrREFBQUE7SUFDZkMsWUFBWUEsNERBQUFBO0lBQ1pDLFlBQVlBLDREQUFBQTtJQUNaQyxJQUFJQSxvREFBQUE7SUFDSkMsS0FBS0EscURBQUFBO0lBQ0xDLFVBQVVBLDBEQUFBQTtJQUNWQyxTQUFTQSx5REFBQUE7SUFDVEMsUUFBUUEsd0RBQUFBO0lBQ1JDLFVBQVVBLDBEQUFBQTtJQUNWQyxXQUFXQSwyREFBQUE7QUFDWCxFQUFDO0FBRU0sTUFBTUUsWUFBWTtJQUFDO1FBQ3RCQyxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNaEMsdURBQVVBO1FBQ2hCaUMsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU0vQix1REFBVUE7UUFDaEJnQyxNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTTlCLHVEQUFVQTtRQUNoQitCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNN0IsdURBQVVBO1FBQ2hCOEIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU01Qix1REFBVUE7UUFDaEI2QixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTTNCLHVEQUFVQTtRQUNoQjRCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNMUIsdURBQVVBO1FBQ2hCMkIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU16Qix1REFBVUE7UUFDaEIwQixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTXhCLHVEQUFVQTtRQUNoQnlCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNdkIsd0RBQVdBO1FBQ2pCd0IsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU10Qix5REFBV0E7UUFDakJ1QixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTXJCLHlEQUFXQTtRQUNqQnNCLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNcEIseURBQVdBO1FBQ2pCcUIsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtJQUNBO1FBQ0lVLElBQUc7UUFDSEMsT0FBTTtRQUNOQyxhQUFZO1FBQ1pDLE9BQU1uQix5REFBV0E7UUFDakJvQixNQUFLQyxLQUFLQyxHQUFHO1FBQ2JDLFVBQVM7UUFDVEMsUUFBTztRQUNQQyxZQUFXbkIsMERBQVlBO0lBQzNCO0lBQ0E7UUFDSVUsSUFBRztRQUNIQyxPQUFNO1FBQ05DLGFBQVk7UUFDWkMsT0FBTWxCLHlEQUFXQTtRQUNqQm1CLE1BQUtDLEtBQUtDLEdBQUc7UUFDYkMsVUFBUztRQUNUQyxRQUFPO1FBQ1BDLFlBQVduQiwwREFBWUE7SUFDM0I7SUFDQTtRQUNJVSxJQUFHO1FBQ0hDLE9BQU07UUFDTkMsYUFBWTtRQUNaQyxPQUFNakIseURBQVdBO1FBQ2pCa0IsTUFBS0MsS0FBS0MsR0FBRztRQUNiQyxVQUFTO1FBQ1RDLFFBQU87UUFDUEMsWUFBV25CLDBEQUFZQTtJQUMzQjtDQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9hc3NldHMuanM/YzUxOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgYmxvZ19waWNfMSBmcm9tICcuL2Jsb2dfcGljXzEucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzIgZnJvbSAnLi9ibG9nX3BpY18yLnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY18zIGZyb20gJy4vYmxvZ19waWNfMy5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfNCBmcm9tICcuL2Jsb2dfcGljXzQucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzUgZnJvbSAnLi9ibG9nX3BpY181LnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY182IGZyb20gJy4vYmxvZ19waWNfNi5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfNyBmcm9tICcuL2Jsb2dfcGljXzcucG5nJztcclxuaW1wb3J0IGJsb2dfcGljXzggZnJvbSAnLi9ibG9nX3BpY184LnBuZyc7XHJcbmltcG9ydCBibG9nX3BpY185IGZyb20gJy4vYmxvZ19waWNfOS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTAgZnJvbSAnLi9ibG9nX3BpY18xMC5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTEgZnJvbSAnLi9ibG9nX3BpY18xMS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTIgZnJvbSAnLi9ibG9nX3BpY18xMi5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTMgZnJvbSAnLi9ibG9nX3BpY18xMy5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTQgZnJvbSAnLi9ibG9nX3BpY18xNC5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTUgZnJvbSAnLi9ibG9nX3BpY18xNS5wbmcnO1xyXG5pbXBvcnQgYmxvZ19waWNfMTYgZnJvbSAnLi9ibG9nX3BpY18xNi5wbmcnO1xyXG5pbXBvcnQgZmFjZWJvb2tfaWNvbiBmcm9tICcuL2ZhY2Vib29rX2ljb24ucG5nJ1xyXG5pbXBvcnQgZ29vZ2xlcGx1c19pY29uIGZyb20gJy4vZ29vZ2xlcGx1c19pY29uLnBuZydcclxuaW1wb3J0IHR3aXR0ZXJfaWNvbiBmcm9tICcuL3R3aXR0ZXJfaWNvbi5wbmcnXHJcbmltcG9ydCBwcm9maWxlX2ljb24gZnJvbSAnLi9wcm9maWxlX2ljb24ucG5nJ1xyXG5pbXBvcnQgbG9nbyBmcm9tICcuL2xvZ28ucG5nJ1xyXG5pbXBvcnQgYXJyb3cgZnJvbSAnLi9hcnJvdy5wbmcnXHJcbmltcG9ydCBsb2dvX2xpZ2h0IGZyb20gJy4vbG9nb19saWdodC5wbmcnXHJcbmltcG9ydCBibG9nX2ljb24gZnJvbSAnLi9ibG9nX2ljb24ucG5nJ1xyXG5pbXBvcnQgYWRkX2ljb24gZnJvbSAnLi9hZGRfaWNvbi5wbmcnXHJcbmltcG9ydCBlbWFpbF9pY29uIGZyb20gJy4vZW1haWxfaWNvbi5wbmcnXHJcbmltcG9ydCB1cGxvYWRfYXJlYSBmcm9tICcuL3VwbG9hZF9hcmVhLnBuZydcclxuXHJcbmV4cG9ydCBjb25zdCBhc3NldHMgPSB7XHJcbiAgICBmYWNlYm9va19pY29uLFxyXG4gICAgZ29vZ2xlcGx1c19pY29uLFxyXG4gICAgdHdpdHRlcl9pY29uLFxyXG4gICAgcHJvZmlsZV9pY29uLFxyXG4gICAgbG9nbyxcclxuICAgIGFycm93LFxyXG4gICAgbG9nb19saWdodCxcclxuICAgIGJsb2dfaWNvbixcclxuICAgIGFkZF9pY29uLFxyXG4gICAgZW1haWxfaWNvbixcclxuICAgIHVwbG9hZF9hcmVhXHJcbiAgICB9XHJcblxyXG4gICAgZXhwb3J0IGNvbnN0IGJsb2dfZGF0YSA9IFt7XHJcbiAgICAgICAgaWQ6MSxcclxuICAgICAgICB0aXRsZTpcIkEgZGV0YWlsZWQgc3RlcCBieSBzdGVwIGd1aWRlIHRvIG1hbmFnZSB5b3VyIGxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIkxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjIsXHJcbiAgICAgICAgdGl0bGU6XCJIb3cgdG8gY3JlYXRlIGFuIGVmZmVjdGl2ZSBzdGFydHVwIHJvYWRtYXAgb3IgaWRlYXNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfMixcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJTdGFydHVwXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6MyxcclxuICAgICAgICB0aXRsZTpcIkxlYXJuaW5nIG5ldyB0ZWNobm9sb2d5IHRvIGJvb3N0IHlvdXIgY2FyZWVyIGluIHNvZnR3YXJlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzMsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjQsXHJcbiAgICAgICAgdGl0bGU6XCJUaXBzIGZvciBnZXR0aW5nIHRoZSBtb3N0IG91dCBvZiBhcHBzIGFuZCBzb2Z0d2FyZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY180LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlRlY2hub2xvZ3lcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo1LFxyXG4gICAgICAgIHRpdGxlOlwiRW5oYW5jaW5nIHlvdXIgc2tpbGxzIGFuZCBjYXB0dXJpbmcgbWVtb3JhYmxlIG1vbWVudHNcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfNSxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJMaWZlc3R5bGVcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo2LFxyXG4gICAgICAgIHRpdGxlOlwiTWF4aW1pemluZyByZXR1cm5zIGJ5IG1pbmltaXppbmcgcmVzb3VyY2VzIGluIHlvdXIgc3RhcnR1cFwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY182LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDo3LFxyXG4gICAgICAgIHRpdGxlOlwiVGVjaG5vbG9neSBmb3IgQ2FyZWVyIGFkdmFuY2VtZW50IGluIGRldmVsb3BtZW50XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzcsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjgsXHJcbiAgICAgICAgdGl0bGU6XCJBIGNvbXByZWhlbnNpdmUgcm9hZG1hcCBmb3IgZWZmZWN0aXZlIGxpZmVzdHlsZSBtYW5hZ2VtZW50XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzgsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiTGlmZXN0eWxlXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6OSxcclxuICAgICAgICB0aXRsZTpcIkFjaGlldmluZyBtYXhpbXVtIHJldHVybnMgd2l0aCBtaW5pbWFsIHJlc291cmNlc1wiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY185LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMCxcclxuICAgICAgICB0aXRsZTpcIkJleW9uZCB0aGUgT3JkaW5hcnk6IENyYWZ0aW5nIFlvdXIgRXhjZXB0aW9uYWwgTGlmZXN0eWxlXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzEwLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIkxpZmVzdHlsZVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjExLFxyXG4gICAgICAgIHRpdGxlOlwiVW52ZWlsaW5nIHRoZSBTZWNyZXRzIG9mIFN1Y2Nlc3NmdWwgU3RhcnR1cHMgaW4gVGVjaG5vbGd5XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzExLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMixcclxuICAgICAgICB0aXRsZTpcIkhvdyB0byBkZXNpZ24gYW4gb25saW5lIExlYXJuaW5nIFBsYXRmb3JtIHRvZGF5XCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzEyLFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlRlY2hub2xvZ3lcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxMyxcclxuICAgICAgICB0aXRsZTpcIlRvbW9ycm93J3MgQWxnb3JpdGhtczogU2hhcGluZyB0aGUgTGFuZHNjYXBlIG9mIEZ1dHVyZSBBSVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xMyxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJTdGFydHVwXCIsXHJcbiAgICAgICAgYXV0aG9yOlwiQWxleCBCZW5uZXR0XCIsXHJcbiAgICAgICAgYXV0aG9yX2ltZzpwcm9maWxlX2ljb25cclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgaWQ6MTQsXHJcbiAgICAgICAgdGl0bGU6XCJCYWxhbmNlICYgQmxpc3M6IE5hdmlnYXRpbmcgTGlmZSdzIEpvdXJuZXkgd2l0aCBTdHlsZVwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOlwiTG9yZW0gSXBzdW0gaXMgc2ltcGx5IGR1bW15IHRleHQgb2YgdGhlIHByaW50aW5nIGFuZCB0eXBlc2V0dGluZyBpbmR1c3RyeS4gTG9yZW0gSXBzdW0gaGFzIGJlZW4gdGhlLi5cIixcclxuICAgICAgICBpbWFnZTpibG9nX3BpY18xNCxcclxuICAgICAgICBkYXRlOkRhdGUubm93KCksXHJcbiAgICAgICAgY2F0ZWdvcnk6XCJMaWZlc3R5bGVcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBpZDoxNSxcclxuICAgICAgICB0aXRsZTpcIkV4cGxvcmluZyB0aGUgRXZvbHV0aW9uIG9mIHNvY2lhbCBuZXR3b3JraW5nIGluIHRoZSBGdXR1cmVcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjpcIkxvcmVtIElwc3VtIGlzIHNpbXBseSBkdW1teSB0ZXh0IG9mIHRoZSBwcmludGluZyBhbmQgdHlwZXNldHRpbmcgaW5kdXN0cnkuIExvcmVtIElwc3VtIGhhcyBiZWVuIHRoZS4uXCIsXHJcbiAgICAgICAgaW1hZ2U6YmxvZ19waWNfMTUsXHJcbiAgICAgICAgZGF0ZTpEYXRlLm5vdygpLFxyXG4gICAgICAgIGNhdGVnb3J5OlwiVGVjaG5vbG9neVwiLFxyXG4gICAgICAgIGF1dGhvcjpcIkFsZXggQmVubmV0dFwiLFxyXG4gICAgICAgIGF1dGhvcl9pbWc6cHJvZmlsZV9pY29uXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGlkOjE2LFxyXG4gICAgICAgIHRpdGxlOlwiU2hhcGluZyB0aGUgRnV0dXJlIG9mIHN0YXR1cCBlY29zeXN0ZW0gaW4gdGhlIHdvcmxkXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246XCJMb3JlbSBJcHN1bSBpcyBzaW1wbHkgZHVtbXkgdGV4dCBvZiB0aGUgcHJpbnRpbmcgYW5kIHR5cGVzZXR0aW5nIGluZHVzdHJ5LiBMb3JlbSBJcHN1bSBoYXMgYmVlbiB0aGUuLlwiLFxyXG4gICAgICAgIGltYWdlOmJsb2dfcGljXzE2LFxyXG4gICAgICAgIGRhdGU6RGF0ZS5ub3coKSxcclxuICAgICAgICBjYXRlZ29yeTpcIlN0YXJ0dXBcIixcclxuICAgICAgICBhdXRob3I6XCJBbGV4IEJlbm5ldHRcIixcclxuICAgICAgICBhdXRob3JfaW1nOnByb2ZpbGVfaWNvblxyXG4gICAgfSxcclxuXSJdLCJuYW1lcyI6WyJibG9nX3BpY18xIiwiYmxvZ19waWNfMiIsImJsb2dfcGljXzMiLCJibG9nX3BpY180IiwiYmxvZ19waWNfNSIsImJsb2dfcGljXzYiLCJibG9nX3BpY183IiwiYmxvZ19waWNfOCIsImJsb2dfcGljXzkiLCJibG9nX3BpY18xMCIsImJsb2dfcGljXzExIiwiYmxvZ19waWNfMTIiLCJibG9nX3BpY18xMyIsImJsb2dfcGljXzE0IiwiYmxvZ19waWNfMTUiLCJibG9nX3BpY18xNiIsImZhY2Vib29rX2ljb24iLCJnb29nbGVwbHVzX2ljb24iLCJ0d2l0dGVyX2ljb24iLCJwcm9maWxlX2ljb24iLCJsb2dvIiwiYXJyb3ciLCJsb2dvX2xpZ2h0IiwiYmxvZ19pY29uIiwiYWRkX2ljb24iLCJlbWFpbF9pY29uIiwidXBsb2FkX2FyZWEiLCJhc3NldHMiLCJibG9nX2RhdGEiLCJpZCIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJpbWFnZSIsImRhdGUiLCJEYXRlIiwibm93IiwiY2F0ZWdvcnkiLCJhdXRob3IiLCJhdXRob3JfaW1nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/assets.js\n");

/***/ }),

/***/ "(ssr)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/cookieUtils */ \"(ssr)/./utils/cookieUtils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CookieConsent = ()=>{\n    const [showConsent, setShowConsent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user has already made a choice\n        const consentCookie = (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.getCookie)(\"cookie-consent\");\n        if (!consentCookie) {\n            // Set a timeout to show the consent popup after 10 seconds\n            const timer = setTimeout(()=>{\n                setShowConsent(true);\n            }, 10000) // 10 seconds\n            ;\n            return ()=>clearTimeout(timer);\n        } else if (consentCookie === \"accepted\") {\n            // If user has accepted cookies, set the necessary cookies\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n            (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        }\n    }, []);\n    const acceptCookies = ()=>{\n        // Set cookie with 1-year expiry\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"accepted\", {\n            expires: 365\n        });\n        // Set other cookies as needed\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setAnalyticsCookies)();\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setFunctionalCookies)();\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Cookie preferences saved\");\n    };\n    const declineCookies = ()=>{\n        // Set cookie to remember user declined\n        (0,_utils_cookieUtils__WEBPACK_IMPORTED_MODULE_3__.setCookie)(\"cookie-consent\", \"declined\", {\n            expires: 365\n        });\n        setShowConsent(false);\n        react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.info(\"Cookies declined. Some features may be limited.\");\n    };\n    if (!showConsent) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-0 left-0 right-0 bg-white shadow-lg z-50 border-t border-gray-200 p-4 md:p-6 animate-fade-in\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row md:items-center justify-between gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold mb-2\",\n                                children: \"We use cookies\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 text-sm md:text-base\",\n                                children: [\n                                    'We use cookies to enhance your browsing experience, serve personalized ads or content, and analyze our traffic. By clicking \"Accept All\", you consent to our use of cookies.',\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/privacy-policy\",\n                                        className: \"text-blue-600 hover:underline ml-1\",\n                                        children: \"Read our Cookie Policy\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: declineCookies,\n                                className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 text-sm md:text-base\",\n                                children: \"Decline\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: acceptCookies,\n                                className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 text-sm md:text-base\",\n                                children: \"Accept All\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CookieConsent.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CookieConsent);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/CookieConsent.jsx\n");

/***/ }),

/***/ "(ssr)/./Components/EmailSubscriptionPopup.jsx":
/*!***********************************************!*\
  !*** ./Components/EmailSubscriptionPopup.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst EmailSubscriptionPopup = ()=>{\n    const [showPopup, setShowPopup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [email, setEmail] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Don't show popup on admin pages\n        if (pathname && pathname.startsWith(\"/admin\")) {\n            return;\n        }\n        // Check if user has already subscribed (permanent dismissal)\n        const hasSubscribed = localStorage.getItem(\"emailSubscribed\");\n        if (hasSubscribed === \"true\") {\n            return;\n        }\n        // Check if user has permanently dismissed the popup (closed it twice)\n        const permanentlyDismissed = localStorage.getItem(\"emailPopupPermanentlyDismissed\");\n        if (permanentlyDismissed === \"true\") {\n            return;\n        }\n        // Check if user has closed the popup recently\n        const lastClosedTime = localStorage.getItem(\"emailPopupLastClosed\");\n        const closeCount = parseInt(localStorage.getItem(\"emailPopupCloseCount\") || \"0\");\n        const now = Date.now();\n        // If user has closed it once and it's been less than 5 minutes, don't show\n        if (lastClosedTime && closeCount >= 1 && now - parseInt(lastClosedTime) < 300000) {\n            return;\n        }\n        // Determine the delay based on whether this is first time or after first close\n        let delay;\n        if (closeCount === 0) {\n            // First time - show after 2 minutes\n            delay = 120000; // 2 minutes = 120000ms\n        } else {\n            // After first close - show after 5 minutes from last close\n            delay = Math.max(0, 300000 - (now - parseInt(lastClosedTime || \"0\")));\n        }\n        const timer = setTimeout(()=>{\n            setShowPopup(true);\n        }, delay);\n        // Cleanup timer on component unmount\n        return ()=>clearTimeout(timer);\n    }, [\n        pathname\n    ]);\n    const handleClose = ()=>{\n        setShowPopup(false);\n        // Get current close count and increment it\n        const currentCloseCount = parseInt(localStorage.getItem(\"emailPopupCloseCount\") || \"0\");\n        const newCloseCount = currentCloseCount + 1;\n        // Update close count and timestamp\n        localStorage.setItem(\"emailPopupCloseCount\", newCloseCount.toString());\n        localStorage.setItem(\"emailPopupLastClosed\", Date.now().toString());\n        // If user has closed it twice, permanently dismiss\n        if (newCloseCount >= 2) {\n            localStorage.setItem(\"emailPopupPermanentlyDismissed\", \"true\");\n        }\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!email) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please enter your email address\");\n            return;\n        }\n        try {\n            setLoading(true);\n            const formData = new FormData();\n            formData.append(\"email\", email);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].post(\"/api/email\", formData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Successfully subscribed to our newsletter!\");\n                setShowPopup(false);\n                setEmail(\"\");\n                // Remember that user has subscribed (permanent dismissal)\n                localStorage.setItem(\"emailSubscribed\", \"true\");\n                // Clear any previous close tracking since they subscribed\n                localStorage.removeItem(\"emailPopupCloseCount\");\n                localStorage.removeItem(\"emailPopupLastClosed\");\n                localStorage.removeItem(\"emailPopupPermanentlyDismissed\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Subscription failed. Please try again.\");\n            }\n        } catch (error) {\n            console.error(\"Subscription error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"An error occurred. Please try again.\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    if (!showPopup) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-2xl max-w-md w-full relative overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleClose,\n                    className: \"absolute top-4 right-4 text-gray-500 hover:text-gray-700 z-10\",\n                    \"aria-label\": \"Close popup\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-6 h-6\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M6 18L18 6M6 6l12 12\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-gray-800 mb-2\",\n                                    children: \"SUBSCRIBE NOW\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 text-sm\",\n                                    children: [\n                                        \"DON'T MISS OUT ON THE LATEST BLOG POSTS\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 54\n                                        }, undefined),\n                                        \"AND OFFERS.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-500 mt-2\",\n                                    children: \"Be the first to get notified.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 132,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        value: email,\n                                        onChange: (e)=>setEmail(e.target.value),\n                                        placeholder: \"Email address\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent\",\n                                        required: true\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: loading,\n                                    className: \"w-full bg-black text-white py-3 px-6 rounded-md hover:bg-gray-800 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: loading ? \"SUBSCRIBING...\" : \"SUBSCRIBE\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 145,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 text-center mt-4\",\n                            children: \"You can unsubscribe at any time. We respect your privacy.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n            lineNumber: 118,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\EmailSubscriptionPopup.jsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (EmailSubscriptionPopup);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./Components/EmailSubscriptionPopup.jsx\n");

/***/ }),

/***/ "(ssr)/./app/profile/page.jsx":
/*!******************************!*\
  !*** ./app/profile/page.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(ssr)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _Assets_assets__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/Assets/assets */ \"(ssr)/./Assets/assets.js\");\n/* harmony import */ var _utils_textUtils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/textUtils */ \"(ssr)/./utils/textUtils.js\");\n/* harmony import */ var react_easy_crop__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-easy-crop */ \"(ssr)/./node_modules/react-easy-crop/index.module.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst ProfilePage = ()=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userData, setUserData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: \"\",\n        email: \"\",\n        profilePicture: \"/default_profile.png\"\n    });\n    const [previewUrl, setPreviewUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"profile\");\n    const [showLogoutConfirm, setShowLogoutConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newProfilePicture, setNewProfilePicture] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [passwordData, setPasswordData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPassword: \"\",\n        newPassword: \"\",\n        confirmPassword: \"\"\n    });\n    const [showPasswordForm, setShowPasswordForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [favorites, setFavorites] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [favoritesLoading, setFavoritesLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [saveSuccess, setSaveSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Add new state for image cropping\n    const [showCropper, setShowCropper] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [cropImage, setCropImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crop, setCrop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [zoom, setZoom] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [croppedAreaPixels, setCroppedAreaPixels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [croppedImage, setCroppedImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Add logout functions\n    const handleLogoutClick = ()=>{\n        setShowLogoutConfirm(true);\n    };\n    const handleLogoutConfirm = ()=>{\n        // Clear auth data from localStorage\n        localStorage.removeItem(\"authToken\");\n        localStorage.removeItem(\"userRole\");\n        localStorage.removeItem(\"userId\");\n        localStorage.removeItem(\"userName\");\n        localStorage.removeItem(\"userProfilePicture\");\n        react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Logged out successfully\");\n        // Add delay before navigation\n        setTimeout(()=>{\n            router.push(\"/\");\n        }, 300);\n    };\n    const handleLogoutCancel = ()=>{\n        setShowLogoutConfirm(false);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is logged in\n        const authToken = localStorage.getItem(\"authToken\");\n        const userId = localStorage.getItem(\"userId\");\n        if (!authToken || !userId) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Please log in to view your profile\");\n            router.push(\"/\");\n            return;\n        }\n        // Fetch user profile data\n        const fetchUserProfile = async ()=>{\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(`/api/profile?userId=${userId}`);\n                if (response.data.success) {\n                    setUserData({\n                        id: response.data.user.id,\n                        email: response.data.user.email,\n                        name: response.data.user.name || \"\",\n                        role: response.data.user.role,\n                        profilePicture: response.data.user.profilePicture\n                    });\n                } else {\n                    react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(response.data.message || \"Failed to load profile\");\n                }\n            } catch (error) {\n                console.error(\"Profile fetch error:\", error);\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load profile data\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchUserProfile();\n        // If favorites tab is active, fetch favorites\n        if (activeTab === \"favorites\") {\n            fetchFavorites(authToken);\n        }\n    }, [\n        router,\n        activeTab\n    ]);\n    const fetchFavorites = async (token)=>{\n        try {\n            setFavoritesLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].get(\"/api/favorites\", {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.data.success) {\n                setFavorites(response.data.favorites || []);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load favorites\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching favorites:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to load favorites\");\n        } finally{\n            setFavoritesLoading(false);\n        }\n    };\n    const removeFavorite = async (blogId)=>{\n        try {\n            const token = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].delete(`/api/favorites?blogId=${blogId}`, {\n                headers: {\n                    Authorization: `Bearer ${token}`\n                }\n            });\n            if (response.data.success) {\n                // Update the favorites list\n                setFavorites(favorites.filter((blog)=>blog._id.toString() !== blogId));\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Removed from favorites\");\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to remove from favorites\");\n            }\n        } catch (error) {\n            console.error(\"Error removing favorite:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to remove from favorites\");\n        }\n    };\n    const handleNameChange = (e)=>{\n        setUserData({\n            ...userData,\n            name: e.target.value\n        });\n    };\n    const handleProfilePictureChange = (e)=>{\n        const file = e.target.files[0];\n        if (file) {\n            // Instead of directly setting the file, show the cropper\n            const reader = new FileReader();\n            reader.onloadend = ()=>{\n                setCropImage(reader.result);\n                setShowCropper(true);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const onCropComplete = (croppedArea, croppedAreaPixels)=>{\n        setCroppedAreaPixels(croppedAreaPixels);\n    };\n    const getCroppedImg = async (imageSrc, pixelCrop)=>{\n        const image = new (next_image__WEBPACK_IMPORTED_MODULE_5___default())();\n        image.src = imageSrc;\n        const canvas = document.createElement(\"canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        canvas.width = pixelCrop.width;\n        canvas.height = pixelCrop.height;\n        ctx.drawImage(image, pixelCrop.x, pixelCrop.y, pixelCrop.width, pixelCrop.height, 0, 0, pixelCrop.width, pixelCrop.height);\n        return new Promise((resolve)=>{\n            canvas.toBlob((blob)=>{\n                resolve(blob);\n            }, \"image/jpeg\");\n        });\n    };\n    const applyCrop = async ()=>{\n        try {\n            if (!croppedAreaPixels) return;\n            const croppedBlob = await getCroppedImg(cropImage, croppedAreaPixels);\n            const croppedImageUrl = URL.createObjectURL(croppedBlob);\n            setPreviewUrl(croppedImageUrl);\n            // Convert blob to file\n            const file = new File([\n                croppedBlob\n            ], \"cropped_profile.jpg\", {\n                type: \"image/jpeg\"\n            });\n            setNewProfilePicture(file);\n            setShowCropper(false);\n        } catch (e) {\n            console.error(\"Error applying crop:\", e);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to crop image\");\n        }\n    };\n    const cancelCrop = ()=>{\n        setShowCropper(false);\n        setCropImage(null);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setSaveSuccess(false); // Reset success state\n        try {\n            const formData = new FormData();\n            formData.append(\"userId\", userData.id);\n            formData.append(\"name\", userData.name);\n            if (newProfilePicture) {\n                formData.append(\"profilePicture\", newProfilePicture);\n            }\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].put(\"/api/profile\", formData);\n            if (response.data.success) {\n                // Update local storage with new profile data\n                localStorage.setItem(\"userName\", response.data.user.name);\n                localStorage.setItem(\"userProfilePicture\", response.data.user.profilePicture);\n                // Update state with new data\n                setUserData({\n                    ...userData,\n                    name: response.data.user.name,\n                    profilePicture: response.data.user.profilePicture\n                });\n                // Clear file input\n                setNewProfilePicture(null);\n                setPreviewUrl(null);\n                // Set success state to show message\n                setSaveSuccess(true);\n                // Show toast\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Profile updated successfully\");\n                // Refresh the page after a 1-second delay\n                setTimeout(()=>{\n                    window.location.reload();\n                }, 1000);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(response.data.message || \"Failed to update profile\");\n            }\n        } catch (error) {\n            console.error(\"Profile update error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"Failed to update profile\");\n        }\n    };\n    // Handle password form input changes\n    const handlePasswordChange = (e)=>{\n        setPasswordData({\n            ...passwordData,\n            [e.target.name]: e.target.value\n        });\n    };\n    // Handle password form submission\n    const handlePasswordSubmit = async (e)=>{\n        e.preventDefault();\n        // Validate passwords match\n        if (passwordData.newPassword !== passwordData.confirmPassword) {\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(\"New passwords do not match\");\n            return;\n        }\n        try {\n            const response = await axios__WEBPACK_IMPORTED_MODULE_8__[\"default\"].put(\"/api/password\", {\n                userId: userData.id,\n                currentPassword: passwordData.currentPassword,\n                newPassword: passwordData.newPassword\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(\"Password updated successfully\");\n                // Reset form\n                setPasswordData({\n                    currentPassword: \"\",\n                    newPassword: \"\",\n                    confirmPassword: \"\"\n                });\n                // Hide form\n                setShowPasswordForm(false);\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(response.data.message || \"Failed to update password\");\n            }\n        } catch (error) {\n            console.error(\"Password update error:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(error.response?.data?.message || \"Failed to update password\");\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex justify-center items-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"Loading profile...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 325,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n            lineNumber: 324,\n            columnNumber: 13\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-200 py-5 px-5 md:px-12 lg:px-28\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                    src: _Assets_assets__WEBPACK_IMPORTED_MODULE_6__.assets.logo,\n                                    width: 180,\n                                    alt: \"Mr.Blogger\",\n                                    className: \"w-[130px] sm:w-auto\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 335,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                href: \"/\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center gap-2 font-medium py-1 px-3 sm:py-3 sm:px-6 border border-black shadow-[-7px_7px_0px_#000000]\",\n                                    children: \"Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 338,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 334,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center my-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl sm:text-5xl font-semibold\",\n                                children: \"Your Profile\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 345,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 text-gray-600\",\n                                children: \"Manage your account and preferences\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 346,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 344,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 333,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-100 py-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-4 max-w-4xl\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white p-6 rounded-lg shadow-md mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex border-b border-gray-300\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(\"profile\"),\n                                            className: `py-3 px-6 font-medium ${activeTab === \"profile\" ? \"border-b-2 border-black text-black\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                            children: \"Profile\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 33\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setActiveTab(\"favorites\"),\n                                            className: `py-3 px-6 font-medium ${activeTab === \"favorites\" ? \"border-b-2 border-black text-black\" : \"text-gray-500 hover:text-gray-700\"}`,\n                                            children: \"My Favorites\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 33\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 354,\n                                columnNumber: 25\n                            }, undefined),\n                            activeTab === \"profile\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                onSubmit: handleSubmit,\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                        src: previewUrl || userData.profilePicture,\n                                                        width: 150,\n                                                        height: 150,\n                                                        alt: \"Profile\",\n                                                        className: \"rounded-full object-cover w-[150px] h-[150px] border-4 border-gray-200\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"profilePicture\",\n                                                        className: \"absolute bottom-0 right-0 bg-black text-white p-2 rounded-full cursor-pointer\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            width: \"20\",\n                                                            height: \"20\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            strokeWidth: \"2\",\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    d: \"M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 49\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                                    cx: \"12\",\n                                                                    cy: \"13\",\n                                                                    r: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 396,\n                                                                    columnNumber: 49\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 45\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        id: \"profilePicture\",\n                                                        onChange: handleProfilePictureChange,\n                                                        className: \"hidden\",\n                                                        accept: \"image/*\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 382,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"Click the camera icon to change your profile picture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-gray-700 font-medium mb-2\",\n                                                children: \"Email\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"email\",\n                                                value: userData.email,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-md bg-gray-100\",\n                                                disabled: true\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mt-1 text-sm text-gray-500\",\n                                                children: \"Email cannot be changed\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-gray-700 font-medium mb-2\",\n                                                children: \"Display Name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"text\",\n                                                value: userData.name,\n                                                onChange: handleNameChange,\n                                                className: \"w-full px-4 py-3 border border-gray-300 rounded-md\",\n                                                placeholder: \"Enter your name\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-4\",\n                                        children: [\n                                            saveSuccess && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"fixed top-5 right-5 p-4 bg-white border-l-4 border-green-500 text-green-700 rounded shadow-lg z-50 max-w-xs animate-fade-in\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"mr-3\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                xmlns: \"http://www.w3.org/2000/svg\",\n                                                                className: \"h-6 w-6\",\n                                                                fill: \"none\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                stroke: \"currentColor\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M5 13l4 4L19 7\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 438,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 53\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 49\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"font-bold\",\n                                                                    children: \"Success!\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 442,\n                                                                    columnNumber: 53\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: \"Profile updated successfully.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 53\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 441,\n                                                            columnNumber: 49\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 435,\n                                                    columnNumber: 45\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"submit\",\n                                                className: \"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors\",\n                                                children: \"Save Changes\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 448,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>setShowPasswordForm(!showPasswordForm),\n                                                className: \"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors\",\n                                                children: \"Change Password\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            userData.role === \"admin\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault(); // Prevent form submission\n                                                    router.push(\"/admin\");\n                                                },\n                                                className: \"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors\",\n                                                children: \"Dashboard\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 464,\n                                                columnNumber: 41\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault(); // Prevent form submission\n                                                    handleLogoutClick();\n                                                },\n                                                className: \"px-6 py-3 bg-red-600 text-white font-medium rounded-md hover:bg-red-700 transition-colors\",\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 476,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: (e)=>{\n                                                    e.preventDefault(); // Prevent form submission\n                                                    router.push(\"/\");\n                                                },\n                                                className: \"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors\",\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 380,\n                                columnNumber: 29\n                            }, undefined) : // Favorites tab content\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: favoritesLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center items-center py-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-black\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 41\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 37\n                                }, undefined) : favorites.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                    children: favorites.map((blog)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-white rounded-lg overflow-hidden shadow-md border border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative h-48\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_5___default()), {\n                                                        src: blog.image || _Assets_assets__WEBPACK_IMPORTED_MODULE_6__.assets.placeholder,\n                                                        alt: blog.title,\n                                                        fill: true,\n                                                        className: \"object-cover\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 53\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start mb-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-block px-2 py-1 text-xs bg-gray-100 rounded-full\",\n                                                                    children: blog.category\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 520,\n                                                                    columnNumber: 57\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>removeFavorite(blog._id),\n                                                                    className: \"text-yellow-500 hover:text-yellow-700\",\n                                                                    title: \"Remove from favorites\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                                        width: \"20\",\n                                                                        height: \"20\",\n                                                                        viewBox: \"0 0 24 24\",\n                                                                        fill: \"currentColor\",\n                                                                        stroke: \"currentColor\",\n                                                                        strokeWidth: \"2\",\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polygon\", {\n                                                                            points: \"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                            lineNumber: 539,\n                                                                            columnNumber: 65\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 61\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 57\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-lg font-semibold mb-2 line-clamp-2\",\n                                                            children: blog.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600 mb-3 line-clamp-3\",\n                                                            dangerouslySetInnerHTML: {\n                                                                __html: (0,_utils_textUtils__WEBPACK_IMPORTED_MODULE_7__.getContentPreview)(blog.description, 120)\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                            href: `/blogs/${blog._id}`,\n                                                            className: \"inline-block px-3 py-1 bg-black text-white text-sm rounded hover:bg-gray-800 transition\",\n                                                            children: \"Read Article\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, blog._id, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 45\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 507,\n                                    columnNumber: 37\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-5xl mb-4\",\n                                            children: \"⭐\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"No favorites yet\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-6\",\n                                            children: \"Start adding blogs to your favorites by clicking the star icon on blog posts.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 41\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                            href: \"/\",\n                                            className: \"inline-block px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition\",\n                                            children: \"Browse Blogs\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                            lineNumber: 565,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 561,\n                                    columnNumber: 37\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 501,\n                                columnNumber: 29\n                            }, undefined),\n                            showPasswordForm && activeTab === \"profile\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 pt-6 border-t border-gray-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Change Password\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 579,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handlePasswordSubmit,\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-gray-700 font-medium mb-2\",\n                                                        children: \"Current Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 582,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        name: \"currentPassword\",\n                                                        value: passwordData.currentPassword,\n                                                        onChange: handlePasswordChange,\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 581,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-gray-700 font-medium mb-2\",\n                                                        children: \"New Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 594,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        name: \"newPassword\",\n                                                        value: passwordData.newPassword,\n                                                        onChange: handlePasswordChange,\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-gray-700 font-medium mb-2\",\n                                                        children: \"Confirm New Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 606,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"password\",\n                                                        name: \"confirmPassword\",\n                                                        value: passwordData.confirmPassword,\n                                                        onChange: handlePasswordChange,\n                                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-md\",\n                                                        required: true\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 607,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 37\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"submit\",\n                                                        className: \"px-6 py-3 bg-black text-white font-medium rounded-md hover:bg-gray-800 transition-colors\",\n                                                        children: \"Update Password\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 618,\n                                                        columnNumber: 41\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        type: \"button\",\n                                                        onClick: ()=>setShowPasswordForm(false),\n                                                        className: \"px-6 py-3 border border-gray-300 text-gray-700 font-medium rounded-md hover:bg-gray-50 transition-colors\",\n                                                        children: \"Cancel\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                        lineNumber: 624,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                                lineNumber: 617,\n                                                columnNumber: 37\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                        lineNumber: 580,\n                                        columnNumber: 33\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 578,\n                                columnNumber: 29\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                        lineNumber: 353,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 352,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 351,\n                columnNumber: 13\n            }, undefined),\n            showLogoutConfirm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-md shadow-lg max-w-sm w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"Confirm Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 643,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mb-6\",\n                            children: \"Are you sure you want to log out? You will need to log in again to access your account.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 644,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutCancel,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLogoutConfirm,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 645,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 642,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 641,\n                columnNumber: 17\n            }, undefined),\n            showCropper && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white p-6 rounded-lg max-w-2xl w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Adjust Profile Picture\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 667,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative h-80 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_easy_crop__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                image: cropImage,\n                                crop: crop,\n                                zoom: zoom,\n                                aspect: 1,\n                                cropShape: \"round\",\n                                onCropChange: setCrop,\n                                onCropComplete: onCropComplete,\n                                onZoomChange: setZoom\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                lineNumber: 669,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 668,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                    children: [\n                                        \"Zoom: \",\n                                        zoom.toFixed(1),\n                                        \"x\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 681,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"range\",\n                                    min: 1,\n                                    max: 3,\n                                    step: 0.1,\n                                    value: zoom,\n                                    onChange: (e)=>setZoom(parseFloat(e.target.value)),\n                                    className: \"w-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 680,\n                            columnNumber: 25\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: applyCrop,\n                                    className: \"px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800\",\n                                    children: \"Apply\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 695,\n                                    columnNumber: 29\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    onClick: cancelCrop,\n                                    className: \"px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100\",\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                                    lineNumber: 702,\n                                    columnNumber: 29\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                            lineNumber: 694,\n                            columnNumber: 25\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                    lineNumber: 666,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\profile\\\\page.jsx\",\n                lineNumber: 665,\n                columnNumber: 17\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProfilePage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/profile/page.jsx\n");

/***/ }),

/***/ "(ssr)/./utils/cookieUtils.js":
/*!******************************!*\
  !*** ./utils/cookieUtils.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCookie: () => (/* binding */ getCookie),\n/* harmony export */   hasAcceptedCookies: () => (/* binding */ hasAcceptedCookies),\n/* harmony export */   removeCookie: () => (/* binding */ removeCookie),\n/* harmony export */   setAnalyticsCookies: () => (/* binding */ setAnalyticsCookies),\n/* harmony export */   setCookie: () => (/* binding */ setCookie),\n/* harmony export */   setFunctionalCookies: () => (/* binding */ setFunctionalCookies)\n/* harmony export */ });\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n\n// Set a cookie\nconst setCookie = (name, value, options = {})=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].set(name, value, options);\n};\n// Get a cookie\nconst getCookie = (name)=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(name);\n};\n// Remove a cookie\nconst removeCookie = (name)=>{\n    js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].remove(name);\n};\n// Check if user has accepted cookies\nconst hasAcceptedCookies = ()=>{\n    return js_cookie__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"cookie-consent\") === \"accepted\";\n};\n// Set analytics cookies (only if user has accepted)\nconst setAnalyticsCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set analytics cookies here\n    // Example: Cookies.set('_ga', 'GA1.2.123456789.1234567890', { expires: 365 })\n    }\n};\n// Set functional cookies (only if user has accepted)\nconst setFunctionalCookies = ()=>{\n    if (hasAcceptedCookies()) {\n    // Set functional cookies here\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/cookieUtils.js\n");

/***/ }),

/***/ "(ssr)/./utils/textUtils.js":
/*!****************************!*\
  !*** ./utils/textUtils.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanDescriptionForPreview: () => (/* binding */ cleanDescriptionForPreview),\n/* harmony export */   getContentPreview: () => (/* binding */ getContentPreview)\n/* harmony export */ });\n// Utility functions for text processing\n/**\n * Clean description for preview by removing image and blog references\n * @param {string} content - The content to clean\n * @returns {string} - Cleaned content\n */ const cleanDescriptionForPreview = (content)=>{\n    if (!content) return \"\";\n    let cleanContent = content;\n    // Remove image references {{image:url|filename}}\n    cleanContent = cleanContent.replace(/\\{\\{image:[^}]+\\}\\}/g, \"\");\n    // Remove blog mentions [[blogId|blogTitle]]\n    cleanContent = cleanContent.replace(/\\[\\[[^\\]]+\\]\\]/g, \"\");\n    // Clean up extra whitespace\n    cleanContent = cleanContent.replace(/\\s+/g, \" \").trim();\n    return cleanContent;\n};\n/**\n * Get a preview of content with a specified length\n * @param {string} content - The content to preview\n * @param {number} maxLength - Maximum length of the preview\n * @returns {string} - Preview content\n */ const getContentPreview = (content, maxLength = 120)=>{\n    const cleanContent = cleanDescriptionForPreview(content);\n    return cleanContent.length > maxLength ? cleanContent.substring(0, maxLength) + \"...\" : cleanContent;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./utils/textUtils.js\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1db334ffb0c5\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vYXBwL2dsb2JhbHMuY3NzP2M5ZWEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIxZGIzMzRmZmIwYzVcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./Components/CookieConsent.jsx":
/*!**************************************!*\
  !*** ./Components/CookieConsent.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\CookieConsent.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./Components/EmailSubscriptionPopup.jsx":
/*!***********************************************!*\
  !*** ./Components/EmailSubscriptionPopup.jsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\Components\EmailSubscriptionPopup.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./app/layout.jsx":
/*!************************!*\
  !*** ./app/layout.jsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.jsx\",\"import\":\"Outfit\",\"arguments\":[{\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"600\",\"700\"]}],\"variableName\":\"outfit\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.jsx\\\",\\\"import\\\":\\\"Outfit\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"weight\\\":[\\\"400\\\",\\\"500\\\",\\\"600\\\",\\\"700\\\"]}],\\\"variableName\\\":\\\"outfit\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/Components/CookieConsent */ \"(rsc)/./Components/CookieConsent.jsx\");\n/* harmony import */ var _Components_EmailSubscriptionPopup__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/Components/EmailSubscriptionPopup */ \"(rsc)/./Components/EmailSubscriptionPopup.jsx\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(rsc)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var react_toastify_dist_ReactToastify_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-toastify/dist/ReactToastify.css */ \"(rsc)/./node_modules/react-toastify/dist/ReactToastify.css\");\n\n\n\n\n\n\n\n// In production, use the pre-built CSS file\nconst isProd = \"development\" === \"production\";\nconst cssPath = isProd ? \"/build/tailwind.css\" : \"./globals.css\";\nconst metadata = {\n    title: \"Mr.Blogger\",\n    description: \"A blog platform by Mr.Blogger\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: isProd && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                    rel: \"stylesheet\",\n                    href: cssPath\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                    lineNumber: 23,\n                    columnNumber: 20\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (next_font_google_target_css_path_app_layout_jsx_import_Outfit_arguments_subsets_latin_weight_400_500_600_700_variableName_outfit___WEBPACK_IMPORTED_MODULE_6___default().className),\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_CookieConsent__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 27,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Components_EmailSubscriptionPopup__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_toastify__WEBPACK_IMPORTED_MODULE_4__.ToastContainer, {\n                        position: \"top-center\",\n                        autoClose: 3000\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\layout.jsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.jsx\n");

/***/ }),

/***/ "(rsc)/./app/profile/page.jsx":
/*!******************************!*\
  !*** ./app/profile/page.jsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\Mr.Blog\blog-app\app\profile\page.jsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(ssr)/./Assets/add_icon.png":
/*!*****************************!*\
  !*** ./Assets/add_icon.png ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/add_icon.17426346.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fadd_icon.17426346.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYWRkX2ljb24ucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLGtNQUFrTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYWRkX2ljb24ucG5nP2EzMDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2FkZF9pY29uLjE3NDI2MzQ2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFkZF9pY29uLjE3NDI2MzQ2LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo4fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/add_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/arrow.png":
/*!**************************!*\
  !*** ./Assets/arrow.png ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/arrow.35bdbbc1.png\",\"height\":16,\"width\":18,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Farrow.35bdbbc1.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":7});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYXJyb3cucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDRMQUE0TCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYXJyb3cucG5nPzYxZjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Fycm93LjM1YmRiYmMxLnBuZ1wiLFwiaGVpZ2h0XCI6MTYsXCJ3aWR0aFwiOjE4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmFycm93LjM1YmRiYmMxLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo3fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/arrow.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_icon.png":
/*!******************************!*\
  !*** ./Assets/blog_icon.png ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_icon.6cf97bbc.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_icon.6cf97bbc.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxvTUFBb00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2Jsb2dfaWNvbi5wbmc/MjJlYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvYmxvZ19pY29uLjZjZjk3YmJjLnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfaWNvbi42Y2Y5N2JiYy5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_1.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_1.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_1.4406e300.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_1.4406e300.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18xLnBuZz9lMTk3Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18xLjQ0MDZlMzAwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEuNDQwNmUzMDAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_1.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_10.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_10.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_10.b87908bf.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_10.b87908bf.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTAucG5nPzA0NzUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEwLmI4NzkwOGJmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEwLmI4NzkwOGJmLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_10.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_11.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_11.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_11.ffa40298.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_11.ffa40298.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTEucG5nPzAxMDUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzExLmZmYTQwMjk4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzExLmZmYTQwMjk4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_11.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_12.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_12.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_12.e5886225.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_12.e5886225.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTIucG5nPzQ3ZGQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEyLmU1ODg2MjI1LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEyLmU1ODg2MjI1LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_12.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_13.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_13.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_13.d4a89f0e.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_13.d4a89f0e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTMucG5nPzJjNDgiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzEzLmQ0YTg5ZjBlLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_13.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_14.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_14.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_14.0d239c78.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_14.0d239c78.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTQucG5nPzMxNzciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE0LjBkMjM5Yzc4LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_14.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_15.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_15.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_15.1d85ee32.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_15.1d85ee32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTUucG5nPzYyOGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE1LjFkODVlZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE1LjFkODVlZTMyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_15.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_16.png":
/*!********************************!*\
  !*** ./Assets/blog_pic_16.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_16.ffb19842.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_16.ffb19842.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDJNQUEyTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvYmxvZ19waWNfMTYucG5nPzU5NGYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL2Jsb2dfcGljXzE2LmZmYjE5ODQyLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzE2LmZmYjE5ODQyLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjo1fTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_16.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_2.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_2.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_2.b986ae66.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_2.b986ae66.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18yLnBuZz9mZWI1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18yLmI5ODZhZTY2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzIuYjk4NmFlNjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_2.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_3.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_3.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_3.5e7fff80.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_3.5e7fff80.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfMy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY18zLnBuZz9kZWRlIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY18zLjVlN2ZmZjgwLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzMuNWU3ZmZmODAucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_3.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_4.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_4.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_4.86f96556.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_4.86f96556.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY180LnBuZz8yMGEyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY180Ljg2Zjk2NTU2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzQuODZmOTY1NTYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_4.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_5.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_5.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_5.144896ce.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_5.144896ce.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY181LnBuZz82YjE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY181LjE0NDg5NmNlLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzUuMTQ0ODk2Y2UucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_5.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_6.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_6.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_6.b530ea03.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_6.b530ea03.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY182LnBuZz84ZWQxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY182LmI1MzBlYTAzLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzYuYjUzMGVhMDMucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_6.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_7.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_7.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_7.3dcf8c5f.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_7.3dcf8c5f.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfNy5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY183LnBuZz9mNGJjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY183LjNkY2Y4YzVmLnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzcuM2RjZjhjNWYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_7.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_8.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_8.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_8.50101226.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_8.50101226.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY184LnBuZz85MGQ2Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY184LjUwMTAxMjI2LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzguNTAxMDEyMjYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_8.png\n");

/***/ }),

/***/ "(ssr)/./Assets/blog_pic_9.png":
/*!*******************************!*\
  !*** ./Assets/blog_pic_9.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/blog_pic_9.56aa9ce8.png\",\"height\":720,\"width\":1280,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fblog_pic_9.56aa9ce8.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvYmxvZ19waWNfOS5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMseU1BQXlNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9ibG9nX3BpY185LnBuZz9lODQyIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9ibG9nX3BpY185LjU2YWE5Y2U4LnBuZ1wiLFwiaGVpZ2h0XCI6NzIwLFwid2lkdGhcIjoxMjgwLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmJsb2dfcGljXzkuNTZhYTljZTgucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/blog_pic_9.png\n");

/***/ }),

/***/ "(ssr)/./Assets/email_icon.png":
/*!*******************************!*\
  !*** ./Assets/email_icon.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/email_icon.4caec7c6.png\",\"height\":32,\"width\":32,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Femail_icon.4caec7c6.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZW1haWxfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsc01BQXNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9lbWFpbF9pY29uLnBuZz82MmY1Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9lbWFpbF9pY29uLjRjYWVjN2M2LnBuZ1wiLFwiaGVpZ2h0XCI6MzIsXCJ3aWR0aFwiOjMyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmVtYWlsX2ljb24uNGNhZWM3YzYucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/email_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/facebook_icon.png":
/*!**********************************!*\
  !*** ./Assets/facebook_icon.png ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/facebook_icon.cbcfc36d.png\",\"height\":58,\"width\":58,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ffacebook_icon.cbcfc36d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZmFjZWJvb2tfaWNvbi5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsNE1BQTRNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9mYWNlYm9va19pY29uLnBuZz85ZDVmIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9mYWNlYm9va19pY29uLmNiY2ZjMzZkLnBuZ1wiLFwiaGVpZ2h0XCI6NTgsXCJ3aWR0aFwiOjU4LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmZhY2Vib29rX2ljb24uY2JjZmMzNmQucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjh9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/facebook_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/googleplus_icon.png":
/*!************************************!*\
  !*** ./Assets/googleplus_icon.png ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/googleplus_icon.15e2de32.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fgoogleplus_icon.15e2de32.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvZ29vZ2xlcGx1c19pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQyxnTkFBZ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL2dvb2dsZXBsdXNfaWNvbi5wbmc/OWM1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvZ29vZ2xlcGx1c19pY29uLjE1ZTJkZTMyLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRmdvb2dsZXBsdXNfaWNvbi4xNWUyZGUzMi5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/googleplus_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo.png":
/*!*************************!*\
  !*** ./Assets/logo.png ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo.c649e147.png\",\"height\":53,\"width\":186,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo.c649e147.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nby5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsMkxBQTJMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvLnBuZz9jOWE0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvLmM2NDllMTQ3LnBuZ1wiLFwiaGVpZ2h0XCI6NTMsXCJ3aWR0aFwiOjE4NixcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvLmM2NDllMTQ3LnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo.png\n");

/***/ }),

/***/ "(ssr)/./Assets/logo_light.png":
/*!*******************************!*\
  !*** ./Assets/logo_light.png ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/logo_light.9ce1f99e.png\",\"height\":55,\"width\":201,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Flogo_light.9ce1f99e.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":2});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvbG9nb19saWdodC5wbmciLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLENBQUMsdU1BQXVNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL0Fzc2V0cy9sb2dvX2xpZ2h0LnBuZz83NWI0Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IHtcInNyY1wiOlwiL19uZXh0L3N0YXRpYy9tZWRpYS9sb2dvX2xpZ2h0LjljZTFmOTllLnBuZ1wiLFwiaGVpZ2h0XCI6NTUsXCJ3aWR0aFwiOjIwMSxcImJsdXJEYXRhVVJMXCI6XCIvX25leHQvaW1hZ2U/dXJsPSUyRl9uZXh0JTJGc3RhdGljJTJGbWVkaWElMkZsb2dvX2xpZ2h0LjljZTFmOTllLnBuZyZ3PTgmcT03MFwiLFwiYmx1cldpZHRoXCI6OCxcImJsdXJIZWlnaHRcIjoyfTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./Assets/logo_light.png\n");

/***/ }),

/***/ "(ssr)/./Assets/profile_icon.png":
/*!*********************************!*\
  !*** ./Assets/profile_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/profile_icon.fa2679c4.png\",\"height\":92,\"width\":92,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fprofile_icon.fa2679c4.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvcHJvZmlsZV9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3Byb2ZpbGVfaWNvbi5wbmc/NmI3NyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvcHJvZmlsZV9pY29uLmZhMjY3OWM0LnBuZ1wiLFwiaGVpZ2h0XCI6OTIsXCJ3aWR0aFwiOjkyLFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnByb2ZpbGVfaWNvbi5mYTI2NzljNC5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/profile_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/twitter_icon.png":
/*!*********************************!*\
  !*** ./Assets/twitter_icon.png ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/twitter_icon.0d1dc581.png\",\"height\":59,\"width\":59,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Ftwitter_icon.0d1dc581.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":8});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdHdpdHRlcl9pY29uLnBuZyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsQ0FBQywwTUFBME0iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9uZXh0LWJsb2ctYXBwLy4vQXNzZXRzL3R3aXR0ZXJfaWNvbi5wbmc/NDI5NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCB7XCJzcmNcIjpcIi9fbmV4dC9zdGF0aWMvbWVkaWEvdHdpdHRlcl9pY29uLjBkMWRjNTgxLnBuZ1wiLFwiaGVpZ2h0XCI6NTksXCJ3aWR0aFwiOjU5LFwiYmx1ckRhdGFVUkxcIjpcIi9fbmV4dC9pbWFnZT91cmw9JTJGX25leHQlMkZzdGF0aWMlMkZtZWRpYSUyRnR3aXR0ZXJfaWNvbi4wZDFkYzU4MS5wbmcmdz04JnE9NzBcIixcImJsdXJXaWR0aFwiOjgsXCJibHVySGVpZ2h0XCI6OH07Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./Assets/twitter_icon.png\n");

/***/ }),

/***/ "(ssr)/./Assets/upload_area.png":
/*!********************************!*\
  !*** ./Assets/upload_area.png ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\"src\":\"/_next/static/media/upload_area.1ee5fe3d.png\",\"height\":140,\"width\":240,\"blurDataURL\":\"/_next/image?url=%2F_next%2Fstatic%2Fmedia%2Fupload_area.1ee5fe3d.png&w=8&q=70\",\"blurWidth\":8,\"blurHeight\":5});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxDQUFDLDBNQUEwTSIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9Bc3NldHMvdXBsb2FkX2FyZWEucG5nP2RhZTMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQge1wic3JjXCI6XCIvX25leHQvc3RhdGljL21lZGlhL3VwbG9hZF9hcmVhLjFlZTVmZTNkLnBuZ1wiLFwiaGVpZ2h0XCI6MTQwLFwid2lkdGhcIjoyNDAsXCJibHVyRGF0YVVSTFwiOlwiL19uZXh0L2ltYWdlP3VybD0lMkZfbmV4dCUyRnN0YXRpYyUyRm1lZGlhJTJGdXBsb2FkX2FyZWEuMWVlNWZlM2QucG5nJnc9OCZxPTcwXCIsXCJibHVyV2lkdGhcIjo4LFwiYmx1ckhlaWdodFwiOjV9OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./Assets/upload_area.png\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL25leHQtYmxvZy1hcHAvLi9hcHAvZmF2aWNvbi5pY28/YWVmZiJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/react-toastify","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/js-cookie","vendor-chunks/proxy-from-env","vendor-chunks/supports-color","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/normalize-wheel","vendor-chunks/tslib","vendor-chunks/react-easy-crop"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fprofile%2Fpage&page=%2Fprofile%2Fpage&appPaths=%2Fprofile%2Fpage&pagePath=private-next-app-dir%2Fprofile%2Fpage.jsx&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();