"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[id]/page",{

/***/ "(app-pages-browser)/./Components/CommentSection.jsx":
/*!***************************************!*\
  !*** ./Components/CommentSection.jsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _CommentForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CommentForm */ \"(app-pages-browser)/./Components/CommentForm.jsx\");\n/* harmony import */ var _CommentItem__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CommentItem */ \"(app-pages-browser)/./Components/CommentItem.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nconst CommentSection = (param)=>{\n    let { blogId, isLoggedIn, onLoginRequired } = param;\n    _s();\n    const [comments, setComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [commentsEnabled, setCommentsEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showComments, setShowComments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [commentsLoaded, setCommentsLoaded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 10,\n        total: 0,\n        pages: 0\n    });\n    // Fetch comments\n    const fetchComments = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].get(\"/api/comments\", {\n                params: {\n                    blogId,\n                    page,\n                    limit: pagination.limit\n                }\n            });\n            if (response.data.success) {\n                setComments(response.data.comments);\n                setPagination(response.data.pagination);\n                setCommentsEnabled(true);\n            } else {\n                if (response.data.message.includes(\"disabled\")) {\n                    setCommentsEnabled(false);\n                }\n                console.error(\"Failed to fetch comments:\", response.data.message);\n            }\n        } catch (error) {\n            var _error_response_data_message, _error_response_data, _error_response;\n            console.error(\"Error fetching comments:\", error);\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_message = _error_response_data.message) === null || _error_response_data_message === void 0 ? void 0 : _error_response_data_message.includes(\"disabled\")) {\n                setCommentsEnabled(false);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle new comment submission\n    const handleCommentSubmit = async (content)=>{\n        if (!isLoggedIn) {\n            onLoginRequired();\n            return;\n        }\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"/api/comments\", {\n                blogId,\n                content\n            }, {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Comment posted successfully!\");\n                // Add new comment to the beginning of the list\n                setComments((prev)=>[\n                        response.data.comment,\n                        ...prev\n                    ]);\n                setPagination((prev)=>({\n                        ...prev,\n                        total: prev.total + 1\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(response.data.message || \"Failed to post comment\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error posting comment:\", error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please log in to comment\");\n                onLoginRequired();\n            } else {\n                var _error_response_data, _error_response1;\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to post comment\");\n            }\n        }\n    };\n    // Handle reply submission\n    const handleReplySubmit = async (parentCommentId, content)=>{\n        if (!isLoggedIn) {\n            onLoginRequired();\n            return;\n        }\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"/api/comments\", {\n                blogId,\n                content,\n                parentCommentId\n            }, {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Reply posted successfully!\");\n                // Add reply to the parent comment\n                setComments((prev)=>prev.map((comment)=>{\n                        if (comment._id === parentCommentId) {\n                            return {\n                                ...comment,\n                                replies: [\n                                    ...comment.replies,\n                                    response.data.comment\n                                ]\n                            };\n                        }\n                        return comment;\n                    }));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(response.data.message || \"Failed to post reply\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error posting reply:\", error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please log in to reply\");\n                onLoginRequired();\n            } else {\n                var _error_response_data, _error_response1;\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to post reply\");\n            }\n        }\n    };\n    // Handle comment reaction\n    const handleReaction = async (commentId, reactionType)=>{\n        if (!isLoggedIn) {\n            onLoginRequired();\n            return;\n        }\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_5__[\"default\"].post(\"/api/comments/\".concat(commentId, \"/react\"), {\n                reactionType\n            }, {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                // Update reaction counts in the comments\n                const updateCommentReactions = (comments)=>{\n                    return comments.map((comment)=>{\n                        if (comment._id === commentId) {\n                            return {\n                                ...comment,\n                                likeCount: response.data.likeCount,\n                                dislikeCount: response.data.dislikeCount,\n                                userReaction: response.data.reaction\n                            };\n                        }\n                        // Also check replies\n                        if (comment.replies && comment.replies.length > 0) {\n                            return {\n                                ...comment,\n                                replies: updateCommentReactions(comment.replies)\n                            };\n                        }\n                        return comment;\n                    });\n                };\n                setComments((prev)=>updateCommentReactions(prev));\n            } else {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(response.data.message || \"Failed to update reaction\");\n            }\n        } catch (error) {\n            var _error_response;\n            console.error(\"Error updating reaction:\", error);\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Please log in to react\");\n                onLoginRequired();\n            } else {\n                var _error_response_data, _error_response1;\n                react_toastify__WEBPACK_IMPORTED_MODULE_2__.toast.error(((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data = _error_response1.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"Failed to update reaction\");\n            }\n        }\n    };\n    // Load more comments\n    const loadMoreComments = ()=>{\n        if (pagination.page < pagination.pages) {\n            fetchComments(pagination.page + 1);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (blogId) {\n            fetchComments();\n        }\n    }, [\n        blogId\n    ]);\n    if (!commentsEnabled) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto mt-12 p-6 bg-gray-50 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xl font-semibold mb-4\",\n                    children: \"Comments\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 209,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Comments are currently disabled for this blog.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 210,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto mt-12 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"text-2xl font-semibold mb-6\",\n                children: [\n                    \"Comments (\",\n                    pagination.total,\n                    \")\"\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 217,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    onSubmit: handleCommentSubmit,\n                    isLoggedIn: isLoggedIn,\n                    onLoginRequired: onLoginRequired,\n                    placeholder: \"Share your thoughts about this blog post...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading comments...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 233,\n                columnNumber: 9\n            }, undefined) : comments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8 bg-gray-50 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"No comments yet. Be the first to share your thoughts!\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                    lineNumber: 239,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                lineNumber: 238,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: comments.map((comment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentItem__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                comment: comment,\n                                onReply: handleReplySubmit,\n                                onReaction: handleReaction,\n                                isLoggedIn: isLoggedIn,\n                                onLoginRequired: onLoginRequired\n                            }, comment._id, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                                lineNumber: 245,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 243,\n                        columnNumber: 11\n                    }, undefined),\n                    pagination.page < pagination.pages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: loadMoreComments,\n                            className: \"px-6 py-2 bg-black text-white border border-black hover:bg-gray-800 transition-colors\",\n                            children: \"Load More Comments\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                            lineNumber: 259,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentSection.jsx\",\n        lineNumber: 216,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CommentSection, \"AIpv8r40MWhsu0Qv74n2TOZOkds=\");\n_c = CommentSection;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CommentSection);\nvar _c;\n$RefreshReg$(_c, \"CommentSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/CommentSection.jsx\n"));

/***/ })

});