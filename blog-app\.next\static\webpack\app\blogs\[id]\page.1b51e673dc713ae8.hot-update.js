"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/blogs/[id]/page",{

/***/ "(app-pages-browser)/./Components/CommentItem.jsx":
/*!************************************!*\
  !*** ./Components/CommentItem.jsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CommentForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CommentForm */ \"(app-pages-browser)/./Components/CommentForm.jsx\");\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst CommentItem = (param)=>{\n    let { comment, onReply, onReaction, isLoggedIn, onLoginRequired, isReply = false } = param;\n    var _comment_userId, _comment_userId1, _comment_userId2;\n    _s();\n    const [showReplyForm, setShowReplyForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [userReaction, setUserReaction] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [likeCount, setLikeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(comment.likeCount || 0);\n    const [dislikeCount, setDislikeCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(comment.dislikeCount || 0);\n    // Fetch user's reaction status for this comment\n    const fetchUserReaction = async ()=>{\n        if (!isLoggedIn) return;\n        try {\n            const authToken = localStorage.getItem(\"authToken\");\n            const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/comments/\".concat(comment._id, \"/react\"), {\n                headers: {\n                    \"Authorization\": \"Bearer \".concat(authToken)\n                }\n            });\n            if (response.data.success) {\n                setUserReaction(response.data.userReaction);\n                setLikeCount(response.data.likeCount);\n                setDislikeCount(response.data.dislikeCount);\n            }\n        } catch (error) {\n            console.error(\"Error fetching user reaction:\", error);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchUserReaction();\n    }, [\n        isLoggedIn,\n        comment._id\n    ]);\n    const handleReplySubmit = async (content)=>{\n        await onReply(comment._id, content);\n        setShowReplyForm(false);\n    };\n    const handleReaction = async (reactionType)=>{\n        await onReaction(comment._id, reactionType);\n        // Update local state\n        const response = await axios__WEBPACK_IMPORTED_MODULE_4__[\"default\"].get(\"/api/comments/\".concat(comment._id, \"/react\"), {\n            headers: {\n                \"Authorization\": \"Bearer \".concat(localStorage.getItem(\"authToken\"))\n            }\n        });\n        if (response.data.success) {\n            setUserReaction(response.data.userReaction);\n            setLikeCount(response.data.likeCount);\n            setDislikeCount(response.data.dislikeCount);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInSeconds = Math.floor((now - date) / 1000);\n        if (diffInSeconds < 60) {\n            return \"Just now\";\n        } else if (diffInSeconds < 3600) {\n            const minutes = Math.floor(diffInSeconds / 60);\n            return \"\".concat(minutes, \" minute\").concat(minutes > 1 ? \"s\" : \"\", \" ago\");\n        } else if (diffInSeconds < 86400) {\n            const hours = Math.floor(diffInSeconds / 3600);\n            return \"\".concat(hours, \" hour\").concat(hours > 1 ? \"s\" : \"\", \" ago\");\n        } else if (diffInSeconds < 604800) {\n            const days = Math.floor(diffInSeconds / 86400);\n            return \"\".concat(days, \" day\").concat(days > 1 ? \"s\" : \"\", \" ago\");\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\".concat(isReply ? \"ml-8 border-l-2 border-gray-200 pl-4\" : \"\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border border-gray-200 rounded-lg p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start gap-3 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                src: ((_comment_userId = comment.userId) === null || _comment_userId === void 0 ? void 0 : _comment_userId.profilePicture) || \"/default_profile.png\",\n                                alt: ((_comment_userId1 = comment.userId) === null || _comment_userId1 === void 0 ? void 0 : _comment_userId1.name) || \"User\",\n                                width: 40,\n                                height: 40,\n                                className: \"rounded-full object-cover w-10 h-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium text-gray-900\",\n                                                children: ((_comment_userId2 = comment.userId) === null || _comment_userId2 === void 0 ? void 0 : _comment_userId2.name) || \"Anonymous User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: formatDate(comment.createdAt)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 whitespace-pre-wrap\",\n                                        children: comment.content\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleReaction(\"like\"),\n                                disabled: !isLoggedIn,\n                                className: \"flex items-center gap-1 px-2 py-1 rounded transition-colors \".concat(userReaction === \"like\" ? \"bg-green-100 text-green-700\" : \"text-gray-600 hover:bg-gray-100\", \" \").concat(!isLoggedIn ? \"cursor-not-allowed opacity-50\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        width: \"16\",\n                                        height: \"16\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: userReaction === \"like\" ? \"currentColor\" : \"none\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"2\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: likeCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleReaction(\"dislike\"),\n                                disabled: !isLoggedIn,\n                                className: \"flex items-center gap-1 px-2 py-1 rounded transition-colors \".concat(userReaction === \"dislike\" ? \"bg-red-100 text-red-700\" : \"text-gray-600 hover:bg-gray-100\", \" \").concat(!isLoggedIn ? \"cursor-not-allowed opacity-50\" : \"\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        width: \"16\",\n                                        height: \"16\",\n                                        viewBox: \"0 0 24 24\",\n                                        fill: userReaction === \"dislike\" ? \"currentColor\" : \"none\",\n                                        stroke: \"currentColor\",\n                                        strokeWidth: \"2\",\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3zm7-13h2.67A2.31 2.31 0 0 1 22 4v7a2.31 2.31 0 0 1-2.33 2H17\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: dislikeCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            !isReply && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>{\n                                    if (!isLoggedIn) {\n                                        onLoginRequired();\n                                        return;\n                                    }\n                                    setShowReplyForm(!showReplyForm);\n                                },\n                                className: \"text-gray-600 hover:text-gray-800 transition-colors\",\n                                children: \"Reply\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, undefined),\n                    showReplyForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CommentForm__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            onSubmit: handleReplySubmit,\n                            isLoggedIn: isLoggedIn,\n                            onLoginRequired: onLoginRequired,\n                            placeholder: \"Write a reply...\",\n                            buttonText: \"Post Reply\",\n                            isReply: true,\n                            onCancel: ()=>setShowReplyForm(false)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                        lineNumber: 186,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, undefined),\n            comment.replies && comment.replies.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 space-y-4\",\n                children: comment.replies.map((reply)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CommentItem, {\n                        comment: reply,\n                        onReply: onReply,\n                        onReaction: onReaction,\n                        isLoggedIn: isLoggedIn,\n                        onLoginRequired: onLoginRequired,\n                        isReply: true\n                    }, reply._id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                        lineNumber: 204,\n                        columnNumber: 13\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\Components\\\\CommentItem.jsx\",\n        lineNumber: 89,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CommentItem, \"snvXe0SJvUDzRQYD02C8DHpY6nU=\");\n_c = CommentItem;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CommentItem);\nvar _c;\n$RefreshReg$(_c, \"CommentItem\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./Components/CommentItem.jsx\n"));

/***/ })

});