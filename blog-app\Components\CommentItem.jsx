'use client'
import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import CommentForm from './CommentForm';
import axios from 'axios';

const CommentItem = ({ 
  comment, 
  onReply, 
  onReaction, 
  isLoggedIn, 
  onLoginRequired,
  isReply = false 
}) => {
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [userReaction, setUserReaction] = useState(null);
  const [likeCount, setLikeCount] = useState(comment.likeCount || 0);
  const [dislikeCount, setDislikeCount] = useState(comment.dislikeCount || 0);

  // Fetch user's reaction status for this comment
  const fetchUserReaction = async () => {
    if (!isLoggedIn) return;

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.get(`/api/comments/${comment._id}/react`, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        setUserReaction(response.data.userReaction);
        setLikeCount(response.data.likeCount);
        setDislikeCount(response.data.dislikeCount);
      }
    } catch (error) {
      console.error('Error fetching user reaction:', error);
    }
  };

  useEffect(() => {
    fetchUserReaction();
  }, [isLoggedIn, comment._id]);

  const handleReplySubmit = async (content) => {
    await onReply(comment._id, content);
    setShowReplyForm(false);
  };

  const handleReaction = async (reactionType) => {
    await onReaction(comment._id, reactionType);
    // Update local state
    const response = await axios.get(`/api/comments/${comment._id}/react`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('authToken')}`
      }
    });
    
    if (response.data.success) {
      setUserReaction(response.data.userReaction);
      setLikeCount(response.data.likeCount);
      setDislikeCount(response.data.dislikeCount);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className={`${isReply ? 'ml-8 border-l-2 border-gray-200 pl-4' : ''}`}>
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        {/* Comment Header */}
        <div className="flex items-start gap-3 mb-3">
          <Image
            src={comment.userId?.profilePicture || '/default_profile.png'}
            alt={comment.userId?.name || 'User'}
            width={40}
            height={40}
            className="rounded-full object-cover w-10 h-10"
          />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="font-medium text-gray-900">
                {comment.userId?.name || 'Anonymous User'}
              </h4>
              <span className="text-sm text-gray-500">
                {formatDate(comment.createdAt)}
              </span>
            </div>
            <p className="text-gray-700 whitespace-pre-wrap">{comment.content}</p>
          </div>
        </div>

        {/* Comment Actions */}
        <div className="flex items-center gap-4 text-sm">
          {/* Like Button */}
          <button
            onClick={() => handleReaction('like')}
            disabled={!isLoggedIn}
            className={`flex items-center gap-1 px-2 py-1 rounded transition-colors ${
              userReaction === 'like'
                ? 'bg-green-100 text-green-700'
                : 'text-gray-600 hover:bg-gray-100'
            } ${!isLoggedIn ? 'cursor-not-allowed opacity-50' : ''}`}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill={userReaction === 'like' ? 'currentColor' : 'none'}
              stroke="currentColor" 
              strokeWidth="2"
            >
              <path d="M7 10v12l4-4 4 4V10l-4-4-4 4z"/>
              <path d="M20 6L9 17l-5-5"/>
            </svg>
            <span>{likeCount}</span>
          </button>

          {/* Dislike Button */}
          <button
            onClick={() => handleReaction('dislike')}
            disabled={!isLoggedIn}
            className={`flex items-center gap-1 px-2 py-1 rounded transition-colors ${
              userReaction === 'dislike'
                ? 'bg-red-100 text-red-700'
                : 'text-gray-600 hover:bg-gray-100'
            } ${!isLoggedIn ? 'cursor-not-allowed opacity-50' : ''}`}
          >
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill={userReaction === 'dislike' ? 'currentColor' : 'none'}
              stroke="currentColor" 
              strokeWidth="2"
              className="rotate-180"
            >
              <path d="M7 10v12l4-4 4 4V10l-4-4-4 4z"/>
              <path d="M20 6L9 17l-5-5"/>
            </svg>
            <span>{dislikeCount}</span>
          </button>

          {/* Reply Button */}
          {!isReply && (
            <button
              onClick={() => {
                if (!isLoggedIn) {
                  onLoginRequired();
                  return;
                }
                setShowReplyForm(!showReplyForm);
              }}
              className="text-gray-600 hover:text-gray-800 transition-colors"
            >
              Reply
            </button>
          )}
        </div>

        {/* Reply Form */}
        {showReplyForm && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <CommentForm
              onSubmit={handleReplySubmit}
              isLoggedIn={isLoggedIn}
              onLoginRequired={onLoginRequired}
              placeholder="Write a reply..."
              buttonText="Post Reply"
              isReply={true}
              onCancel={() => setShowReplyForm(false)}
            />
          </div>
        )}
      </div>

      {/* Replies */}
      {comment.replies && comment.replies.length > 0 && (
        <div className="mt-4 space-y-4">
          {comment.replies.map((reply) => (
            <CommentItem
              key={reply._id}
              comment={reply}
              onReply={onReply}
              onReaction={onReaction}
              isLoggedIn={isLoggedIn}
              onLoginRequired={onLoginRequired}
              isReply={true}
            />
          ))}
        </div>
      )}
    </div>
  );
};

export default CommentItem;
