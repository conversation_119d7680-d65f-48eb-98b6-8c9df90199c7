"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/comments/settings/route";
exports.ids = ["app/api/comments/settings/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Fsettings%2Froute&page=%2Fapi%2Fcomments%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Fsettings%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Fsettings%2Froute&page=%2Fapi%2Fcomments%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Fsettings%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_comments_settings_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/comments/settings/route.js */ \"(rsc)/./app/api/comments/settings/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/comments/settings/route\",\n        pathname: \"/api/comments/settings\",\n        filename: \"route\",\n        bundlePath: \"app/api/comments/settings/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\comments\\\\settings\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_comments_settings_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/comments/settings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Fsettings%2Froute&page=%2Fapi%2Fcomments%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Fsettings%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/comments/settings/route.js":
/*!********************************************!*\
  !*** ./app/api/comments/settings/route.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n/* harmony import */ var _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/models/CommentSettingsModel */ \"(rsc)/./lib/models/CommentSettingsModel.js\");\n\n\n\n\n// Helper function to get user from token\nfunction getUserFromToken(request) {\n    const authHeader = request.headers.get(\"Authorization\");\n    if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n        return null;\n    }\n    const token = authHeader.split(\" \")[1];\n    return (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_2__.verifyToken)(token);\n}\n// Helper function to check if user is admin\nfunction isAdmin(userData) {\n    return userData && userData.role === \"admin\";\n}\n// GET - Fetch comment settings\nasync function GET(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const { searchParams } = new URL(request.url);\n        const blogId = searchParams.get(\"blogId\");\n        let settings;\n        if (blogId) {\n            // Get blog-specific settings\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                blogId\n            });\n            if (!settings) {\n                // Fall back to global settings\n                settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                    blogId: null\n                });\n            }\n        } else {\n            // Get global settings\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n                blogId: null\n            });\n        }\n        // If no settings exist, return defaults\n        if (!settings) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: true,\n                settings: {\n                    commentsEnabled: true,\n                    requireLogin: true,\n                    allowReplies: true,\n                    allowReactions: true,\n                    maxCommentLength: 1000,\n                    moderationEnabled: false\n                }\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            settings: {\n                commentsEnabled: settings.commentsEnabled,\n                requireLogin: settings.requireLogin,\n                allowReplies: settings.allowReplies,\n                allowReactions: settings.allowReactions,\n                maxCommentLength: settings.maxCommentLength,\n                moderationEnabled: settings.moderationEnabled,\n                blogId: settings.blogId,\n                updatedAt: settings.updatedAt\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching comment settings:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch comment settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n// POST - Update comment settings (Admin only)\nasync function POST(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const userData = getUserFromToken(request);\n        if (!userData || !isAdmin(userData)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Admin access required\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { blogId = null, commentsEnabled, requireLogin, allowReplies, allowReactions, maxCommentLength, moderationEnabled } = body;\n        // Validate maxCommentLength\n        if (maxCommentLength && (maxCommentLength < 100 || maxCommentLength > 5000)) {\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Max comment length must be between 100 and 5000 characters\"\n            }, {\n                status: 400\n            });\n        }\n        // Find existing settings or create new ones\n        let settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].findOne({\n            blogId\n        });\n        if (settings) {\n            // Update existing settings\n            if (commentsEnabled !== undefined) settings.commentsEnabled = commentsEnabled;\n            if (requireLogin !== undefined) settings.requireLogin = requireLogin;\n            if (allowReplies !== undefined) settings.allowReplies = allowReplies;\n            if (allowReactions !== undefined) settings.allowReactions = allowReactions;\n            if (maxCommentLength !== undefined) settings.maxCommentLength = maxCommentLength;\n            if (moderationEnabled !== undefined) settings.moderationEnabled = moderationEnabled;\n            settings.updatedBy = userData.userId;\n            await settings.save();\n        } else {\n            // Create new settings\n            settings = await _lib_models_CommentSettingsModel__WEBPACK_IMPORTED_MODULE_3__[\"default\"].create({\n                blogId,\n                commentsEnabled: commentsEnabled !== undefined ? commentsEnabled : true,\n                requireLogin: requireLogin !== undefined ? requireLogin : true,\n                allowReplies: allowReplies !== undefined ? allowReplies : true,\n                allowReactions: allowReactions !== undefined ? allowReactions : true,\n                maxCommentLength: maxCommentLength || 1000,\n                moderationEnabled: moderationEnabled !== undefined ? moderationEnabled : false,\n                updatedBy: userData.userId\n            });\n        }\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true,\n            message: \"Comment settings updated successfully\",\n            settings: {\n                commentsEnabled: settings.commentsEnabled,\n                requireLogin: settings.requireLogin,\n                allowReplies: settings.allowReplies,\n                allowReactions: settings.allowReactions,\n                maxCommentLength: settings.maxCommentLength,\n                moderationEnabled: settings.moderationEnabled,\n                blogId: settings.blogId,\n                updatedAt: settings.updatedAt\n            }\n        });\n    } catch (error) {\n        console.error(\"Error updating comment settings:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to update comment settings\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/comments/settings/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\";\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/CommentSettingsModel.js":
/*!********************************************!*\
  !*** ./lib/models/CommentSettingsModel.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst CommentSettingsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Global settings\n    commentsEnabled: {\n        type: Boolean,\n        default: true\n    },\n    requireLogin: {\n        type: Boolean,\n        default: true\n    },\n    allowReplies: {\n        type: Boolean,\n        default: true\n    },\n    allowReactions: {\n        type: Boolean,\n        default: true\n    },\n    maxCommentLength: {\n        type: Number,\n        default: 1000,\n        min: 100,\n        max: 5000\n    },\n    moderationEnabled: {\n        type: Boolean,\n        default: false\n    },\n    // Per-blog settings (optional)\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blog\",\n        default: null,\n        index: true\n    },\n    updatedAt: {\n        type: Date,\n        default: Date.now\n    },\n    updatedBy: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"user\",\n        required: true\n    }\n});\n// Update the updatedAt field before saving\nCommentSettingsSchema.pre(\"save\", function(next) {\n    this.updatedAt = Date.now();\n    next();\n});\n// Ensure only one global setting (where blogId is null)\nCommentSettingsSchema.index({\n    blogId: 1\n}, {\n    unique: true,\n    sparse: true\n});\nconst CommentSettingsModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).CommentSettings || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"CommentSettings\", CommentSettingsSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CommentSettingsModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvbW9kZWxzL0NvbW1lbnRTZXR0aW5nc01vZGVsLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnQztBQUVoQyxNQUFNQyx3QkFBd0IsSUFBSUQsd0RBQWUsQ0FBQztJQUNoRCxrQkFBa0I7SUFDbEJHLGlCQUFpQjtRQUNmQyxNQUFNQztRQUNOQyxTQUFTO0lBQ1g7SUFDQUMsY0FBYztRQUNaSCxNQUFNQztRQUNOQyxTQUFTO0lBQ1g7SUFDQUUsY0FBYztRQUNaSixNQUFNQztRQUNOQyxTQUFTO0lBQ1g7SUFDQUcsZ0JBQWdCO1FBQ2RMLE1BQU1DO1FBQ05DLFNBQVM7SUFDWDtJQUNBSSxrQkFBa0I7UUFDaEJOLE1BQU1PO1FBQ05MLFNBQVM7UUFDVE0sS0FBSztRQUNMQyxLQUFLO0lBQ1A7SUFDQUMsbUJBQW1CO1FBQ2pCVixNQUFNQztRQUNOQyxTQUFTO0lBQ1g7SUFDQSwrQkFBK0I7SUFDL0JTLFFBQVE7UUFDTlgsTUFBTUosd0RBQWUsQ0FBQ2dCLEtBQUssQ0FBQ0MsUUFBUTtRQUNwQ0MsS0FBSztRQUNMWixTQUFTO1FBQ1RhLE9BQU87SUFDVDtJQUNBQyxXQUFXO1FBQ1RoQixNQUFNaUI7UUFDTmYsU0FBU2UsS0FBS0MsR0FBRztJQUNuQjtJQUNBQyxXQUFXO1FBQ1RuQixNQUFNSix3REFBZSxDQUFDZ0IsS0FBSyxDQUFDQyxRQUFRO1FBQ3BDQyxLQUFLO1FBQ0xNLFVBQVU7SUFDWjtBQUNGO0FBRUEsMkNBQTJDO0FBQzNDdkIsc0JBQXNCd0IsR0FBRyxDQUFDLFFBQVEsU0FBU0MsSUFBSTtJQUM3QyxJQUFJLENBQUNOLFNBQVMsR0FBR0MsS0FBS0MsR0FBRztJQUN6Qkk7QUFDRjtBQUVBLHdEQUF3RDtBQUN4RHpCLHNCQUFzQmtCLEtBQUssQ0FBQztJQUFFSixRQUFRO0FBQUUsR0FBRztJQUFFWSxRQUFRO0lBQU1DLFFBQVE7QUFBSztBQUV4RSxNQUFNQyx1QkFBdUI3Qix3REFBZSxDQUFDK0IsZUFBZSxJQUFJL0IscURBQWMsQ0FBQyxtQkFBbUJDO0FBRWxHLGlFQUFlNEIsb0JBQW9CQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi9tb2RlbHMvQ29tbWVudFNldHRpbmdzTW9kZWwuanM/ZjNjNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbW9uZ29vc2UgZnJvbSBcIm1vbmdvb3NlXCI7XG5cbmNvbnN0IENvbW1lbnRTZXR0aW5nc1NjaGVtYSA9IG5ldyBtb25nb29zZS5TY2hlbWEoe1xuICAvLyBHbG9iYWwgc2V0dGluZ3NcbiAgY29tbWVudHNFbmFibGVkOiB7XG4gICAgdHlwZTogQm9vbGVhbixcbiAgICBkZWZhdWx0OiB0cnVlXG4gIH0sXG4gIHJlcXVpcmVMb2dpbjoge1xuICAgIHR5cGU6IEJvb2xlYW4sXG4gICAgZGVmYXVsdDogdHJ1ZVxuICB9LFxuICBhbGxvd1JlcGxpZXM6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IHRydWVcbiAgfSxcbiAgYWxsb3dSZWFjdGlvbnM6IHtcbiAgICB0eXBlOiBCb29sZWFuLFxuICAgIGRlZmF1bHQ6IHRydWVcbiAgfSxcbiAgbWF4Q29tbWVudExlbmd0aDoge1xuICAgIHR5cGU6IE51bWJlcixcbiAgICBkZWZhdWx0OiAxMDAwLFxuICAgIG1pbjogMTAwLFxuICAgIG1heDogNTAwMFxuICB9LFxuICBtb2RlcmF0aW9uRW5hYmxlZDoge1xuICAgIHR5cGU6IEJvb2xlYW4sXG4gICAgZGVmYXVsdDogZmFsc2VcbiAgfSxcbiAgLy8gUGVyLWJsb2cgc2V0dGluZ3MgKG9wdGlvbmFsKVxuICBibG9nSWQ6IHtcbiAgICB0eXBlOiBtb25nb29zZS5TY2hlbWEuVHlwZXMuT2JqZWN0SWQsXG4gICAgcmVmOiBcImJsb2dcIixcbiAgICBkZWZhdWx0OiBudWxsLFxuICAgIGluZGV4OiB0cnVlXG4gIH0sXG4gIHVwZGF0ZWRBdDoge1xuICAgIHR5cGU6IERhdGUsXG4gICAgZGVmYXVsdDogRGF0ZS5ub3dcbiAgfSxcbiAgdXBkYXRlZEJ5OiB7XG4gICAgdHlwZTogbW9uZ29vc2UuU2NoZW1hLlR5cGVzLk9iamVjdElkLFxuICAgIHJlZjogXCJ1c2VyXCIsXG4gICAgcmVxdWlyZWQ6IHRydWVcbiAgfVxufSk7XG5cbi8vIFVwZGF0ZSB0aGUgdXBkYXRlZEF0IGZpZWxkIGJlZm9yZSBzYXZpbmdcbkNvbW1lbnRTZXR0aW5nc1NjaGVtYS5wcmUoJ3NhdmUnLCBmdW5jdGlvbihuZXh0KSB7XG4gIHRoaXMudXBkYXRlZEF0ID0gRGF0ZS5ub3coKTtcbiAgbmV4dCgpO1xufSk7XG5cbi8vIEVuc3VyZSBvbmx5IG9uZSBnbG9iYWwgc2V0dGluZyAod2hlcmUgYmxvZ0lkIGlzIG51bGwpXG5Db21tZW50U2V0dGluZ3NTY2hlbWEuaW5kZXgoeyBibG9nSWQ6IDEgfSwgeyB1bmlxdWU6IHRydWUsIHNwYXJzZTogdHJ1ZSB9KTtcblxuY29uc3QgQ29tbWVudFNldHRpbmdzTW9kZWwgPSBtb25nb29zZS5tb2RlbHMuQ29tbWVudFNldHRpbmdzIHx8IG1vbmdvb3NlLm1vZGVsKCdDb21tZW50U2V0dGluZ3MnLCBDb21tZW50U2V0dGluZ3NTY2hlbWEpO1xuXG5leHBvcnQgZGVmYXVsdCBDb21tZW50U2V0dGluZ3NNb2RlbDtcbiJdLCJuYW1lcyI6WyJtb25nb29zZSIsIkNvbW1lbnRTZXR0aW5nc1NjaGVtYSIsIlNjaGVtYSIsImNvbW1lbnRzRW5hYmxlZCIsInR5cGUiLCJCb29sZWFuIiwiZGVmYXVsdCIsInJlcXVpcmVMb2dpbiIsImFsbG93UmVwbGllcyIsImFsbG93UmVhY3Rpb25zIiwibWF4Q29tbWVudExlbmd0aCIsIk51bWJlciIsIm1pbiIsIm1heCIsIm1vZGVyYXRpb25FbmFibGVkIiwiYmxvZ0lkIiwiVHlwZXMiLCJPYmplY3RJZCIsInJlZiIsImluZGV4IiwidXBkYXRlZEF0IiwiRGF0ZSIsIm5vdyIsInVwZGF0ZWRCeSIsInJlcXVpcmVkIiwicHJlIiwibmV4dCIsInVuaXF1ZSIsInNwYXJzZSIsIkNvbW1lbnRTZXR0aW5nc01vZGVsIiwibW9kZWxzIiwiQ29tbWVudFNldHRpbmdzIiwibW9kZWwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/CommentSettingsModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/lodash.includes","vendor-chunks/jws","vendor-chunks/jwa","vendor-chunks/lodash.once","vendor-chunks/lodash.isinteger","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isboolean","vendor-chunks/safe-buffer","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fcomments%2Fsettings%2Froute&page=%2Fapi%2Fcomments%2Fsettings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fcomments%2Fsettings%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();